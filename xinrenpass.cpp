#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// 定义账户结构体
typedef struct Account {
    int account_id;      // 账户ID
    char name[50];       // 账户名
    float balance;       // 账户余额
    struct Account* next; // 下一个账户
} Account;

// 全局变量：头指针
Account* head = NULL;

// 创建账户
void create_account(int id, const char* name, float balance) {
    Account* new_account = (Account*)malloc(sizeof(Account));
    new_account->account_id = id;
    strcpy(new_account->name, name);
    new_account->balance = balance;
    new_account->next = head;
    head = new_account;
    printf("账户创建成功！\n");
}

// 存款
void deposit(int id, float amount) {
    Account* temp = head;
    while (temp != NULL) {
        if (temp->account_id == id) {
            temp->balance += amount;
            printf("存款成功！新余额：%.2f\n", temp->balance);
            return;
        }
        temp = temp->next;
    }
    printf("账户未找到！\n");
}

// 取款
void withdraw(int id, float amount) {
    Account* temp = head;
    while (temp != NULL) {
        if (temp->account_id == id) {
            if (temp->balance >= amount) {
                temp->balance -= amount;
                printf("取款成功！新余额：%.2f\n", temp->balance);
            } else {
                printf("余额不足！\n");
            }
            return;
        }
        temp = temp->next;
    }
    printf("账户未找到！\n");
}

// 转账
void transfer(int from_id, int to_id, float amount) {
    Account* from_account = NULL;
    Account* to_account = NULL;
    Account* temp = head;
    
    // 查找转出账户和转入账户
    while (temp != NULL) {
        if (temp->account_id == from_id) {
            from_account = temp;
        }
        if (temp->account_id == to_id) {
            to_account = temp;
        }
        temp = temp->next;
    }
    
    if (from_account && to_account) {
        if (from_account->balance >= amount) {
            from_account->balance -= amount;
            to_account->balance += amount;
            printf("转账成功！\n");
            printf("转出账户余额：%.2f\n", from_account->balance);
            printf("转入账户余额：%.2f\n", to_account->balance);
        } else {
            printf("余额不足，转账失败！\n");
        }
    } else {
        printf("账户未找到！\n");
    }
}

// 查看账户信息
void view_account(int id) {
    Account* temp = head;
    while (temp != NULL) {
        if (temp->account_id == id) {
            printf("账户ID: %d\n", temp->account_id);
            printf("账户名: %s\n", temp->name);
            printf("账户余额: %.2f\n", temp->balance);
            return;
        }
        temp = temp->next;
    }
    printf("账户未找到！\n");
}

// 删除账户
void delete_account(int id) {
    Account* temp = head;
    Account* prev = NULL;
    
    while (temp != NULL) {
        if (temp->account_id == id) {
            if (prev == NULL) {
                head = temp->next;
            } else {
                prev->next = temp->next;
            }
            free(temp);
            printf("账户已删除！\n");
            return;
        }
        prev = temp;
        temp = temp->next;
    }
    printf("账户未找到！\n");
}

// 保存账户数据到文件
void save_to_file() {
    FILE* file = fopen("accounts.txt", "w");
    Account* temp = head;
    while (temp != NULL) {
        fprintf(file, "%d %s %.2f\n", temp->account_id, temp->name, temp->balance);
        temp = temp->next;
    }
    fclose(file);
}

// 从文件加载账户数据
void load_from_file() {
    FILE* file = fopen("accounts.txt", "r");
    if (file == NULL) return;

    while (1) {
        Account* new_account = (Account*)malloc(sizeof(Account));
        int result = fscanf(file, "%d %s %f", &new_account->account_id, new_account->name, &new_account->balance);
        if (result != 3) {
            free(new_account); // 如果读取不完全，释放内存
            break;
        }
        new_account->next = head;
        head = new_account;
    }
    fclose(file);
}

// 显示菜单
void menu() {
    printf("银行账户管理系统\n");
    printf("1. 创建账户\n");
    printf("2. 存款\n");
    printf("3. 取款\n");
    printf("4. 转账\n");
    printf("5. 查看账户\n");
    printf("6. 删除账户\n");
    printf("7. 退出\n");
}

// 释放所有账户内存
void free_all_accounts() {
    Account* temp;
    while (head != NULL) {
        temp = head;
        head = head->next;
        free(temp);
    }
}

int main() {
    load_from_file();  // 加载已有账户数据
    
    int choice;
    while (1) {
        menu();
        printf("请输入操作选项: ");
        scanf("%d", &choice);
        
        if (choice == 1) {
            int id;
            char name[50];
            float balance;
            printf("请输入账户ID: ");
            scanf("%d", &id);
            printf("请输入账户名: ");
            scanf("%s", name);
            printf("请输入初始余额: ");
            scanf("%f", &balance);
            create_account(id, name, balance);
        } else if (choice == 2) {
            int id;
            float amount;
            printf("请输入账户ID: ");
            scanf("%d", &id);
            printf("请输入存款金额: ");
            scanf("%f", &amount);
            deposit(id, amount);
        } else if (choice == 3) {
            int id;
            float amount;
            printf("请输入账户ID: ");
            scanf("%d", &id);
            printf("请输入取款金额: ");
            scanf("%f", &amount);
            withdraw(id, amount);
        } else if (choice == 4) {
            int from_id, to_id;
            float amount;
            printf("请输入转出账户ID: ");
            scanf("%d", &from_id);
            printf("请输入转入账户ID: ");
            scanf("%d", &to_id);
            printf("请输入转账金额: ");
            scanf("%f", &amount);
            transfer(from_id, to_id, amount);
        } else if (choice == 5) {
            int id;
            printf("请输入账户ID: ");
            scanf("%d", &id);
            view_account(id);
        } else if (choice == 6) {
            int id;
            printf("请输入账户ID: ");
            scanf("%d", &id);
            delete_account(id);
        } else if (choice == 7) {
            save_to_file(); // 保存数据
            free_all_accounts(); // 释放所有内存
            printf("退出系统。\n");
            break;
        } else {
            printf("无效的选择！\n");
        }
    }
    
    return 0;
}
