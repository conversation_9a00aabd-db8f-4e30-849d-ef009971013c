#include<stdio.h>
/* #pragma warning(disable:4996) */
#include<string.h>
#include <stdlib.h>
void othermenu();
void menu();
void account_list();
void account_add();
void account_save(int countsize);
void menufind();
void init();
void account_alter();
void account_del();
void account_min(); 
void account_max();
void account_sum();
struct Account {
	int accNum;
	float money;
	struct Account* next;
	int id;
};
Account ac[100];
int count[100];
int main() {
    init();
    int n = 1;
    int countsize = 0; // 将countsize声明移到switch语句之前
    while (n != 0)
    {
        menu();
        int index = 0;
        scanf("%d", &index);
        switch (index)
        {
        case 1:
            account_add();
            break;
        case 2:
            menufind();
            break;
        case 3:
            account_alter();
            break;
        case 4:
            account_del();
            break;
        case 5:
            countsize = 0;
            for (int i = 0; i < 100; i++) {
                if (count[i] == 1) {
                    countsize++;
                }
            }
            account_save(countsize);
            break;
        case 6:
            othermenu();
            break;
        case 0:
            n = 0;
            break;
        default:
            break;
        }
    }

}
Account* head = NULL;
int account_read() {
    FILE* fp = fopen("save.txt", "r");
    if (fp == NULL) {
        printf("无法打开文件 save.txt\n");     
        return -1;
    }

    int judge = 0;
    int accnum = 0;
    float m = -1.0;
    Account* pre = head, * pre1 = NULL;

    while (!feof(fp)) {
        char buffer[100] = "";
        fscanf(fp, "%s", buffer);
        int temp = judge % 2;

        switch (temp) {
        case 0:
            accnum = atoi(buffer);
            break;
        case 1:
            m = (float)atof(buffer);
            break;
        default:
            break;
        }

        if ((judge + 1) % 2 == 0) {
            int i = judge / 2;
            ac[i].accNum = accnum;
            ac[i].money = m;
            if (i == 0) {
                head = &ac[i];
                count[i] = 1;
                pre = head;
            } else {
                pre1 = &ac[i];
                count[i] = 1;
                pre->next = pre1;
                pre = pre1;
                pre->next = NULL;
                pre1 = NULL;
            }
        }

        judge += 1;
    }

    fclose(fp);
    return 0;
}
void init() {
	for(int i=0;i<100;i++)
	ac[i].id=i;
	account_read();
}
void menu() {
	printf("********************************\n");
	printf("*-《欢迎使用个人银行账户管理系统》-*\n");
	printf("*--------1、录入新的账户-------*\n");
	printf("*--------2、账户余额查询-------*\n");
	printf("*--------3、账户余额修改-------*\n");
	printf("*--------4、删除账户-------*\n");
	printf("*--------5、账户余额保存-------*\n");
	printf("*--------6、高级选项-------*\n");
	printf("*--------0、退       出--------*\n");
	printf("*------------------------------*\n");
	printf("*------请输入你的选择！！！----*\n");
	printf("********************************\n");
}


void account_con() {
	printf("------请输入查询的账户的卡号------\n");
	int idnum = 0;
	scanf("%d",&idnum);
	Account* pre = head;
	while (pre != NULL && pre->accNum != idnum) {
		pre = pre->next;
	}
	if (pre != NULL && pre->accNum == idnum) {
		printf("%d\t%.2f\n", pre->accNum,pre->money);
		printf("------      查询成功      ------\n");
	}
	else if (pre == NULL) {
		printf("不存在该账户！！！\n");
		system("pause");
	}
}
void account_list() {
	if (head == NULL) {
		printf("账户系统为空！\n");
	}
	else {
		printf("---------   查询结果   ---------\n");
		Account* p = head;
		printf("卡号\t余额\n");
		while (p != NULL) {
			printf("%d\t%.2f\n", p->accNum,p->money);
			p = p->next;
		}
	}
	system("pause");
}
void menufind() {
	printf("------欢迎来到查询功能模块------\n");
	printf("-请输入查询方式(1:卡号查询,2:全部查询)-\n");
	int optfind = 0;
	scanf("%d", &optfind);
	switch (optfind)
	{
	case 1:
		account_con();
		break;
	case 2:
		account_list();
		break;
	default:
		break;
	}

}
int findvoid(int a[], int size) {
	int i = 0;
	while (i < size && a[i] != 0) {
		i += 1;
	}
	return i;
}
void account_add() {
	printf("------欢迎来到账户创建模块------\n");
	if (head == NULL) {
		head = ac;
		printf("请输入账户卡号......\n");
		int a1 = scanf("%d", &head->accNum);
		getchar();
		printf("请输入余额......\n");
		int a4 = scanf("%f", &head->money);
		count[0] = 1;
	}
	else if (head != NULL) {
		Account* temp = NULL;
		Account* pre = head;
		int idx = 0;
		while (pre->next != NULL) {
			pre = pre->next;
		}
		idx = findvoid(count, 100);
		if (idx == 100) {
			printf("系统账户已满！！！\n");
			system("pause");
		}
		else {
			temp = &ac[idx];
			printf("请输入账户卡号......\n");
			int a1 = scanf("%d", &temp->accNum);
			getchar();
			printf("请输入余额......\n");
			int a4 = scanf("%f", &temp->money);
			count[idx] = 1;
			pre->next = temp;
			temp->next = NULL;
		}

	}
	printf("-------   账户创建成功   -------\n");
}
void account_del() {
	printf("请输入你想要删除账户的卡号......\n");
	int idnum = 0;
	int s = scanf("%d", &idnum);
	if (head->accNum == idnum) {
		count[head->id] = 0;
		head = head->next;
		printf("删除成功！！！\n");
		system("pause");

	}
	else {
		Account* pre = head;
		Account* temp = head->next;
		while (temp != NULL && temp->accNum != idnum) {
			pre = temp;
			temp = temp->next;
		}
		if (temp != NULL && temp->accNum == idnum) {
			pre->next = temp->next;
			temp->next = NULL;
			count[temp->id] = 0;
			printf("删除成功！！！\n");
			system("pause");
		}
		else if (temp == NULL) {
			printf("系统中不存在该账户！！！\n");
			system("pause");
		}
	}
}
void account_alter() {
	printf("您已经进入修改页面！！！\n");
	printf("请您输入要修改账户的卡号......\n");
	int accidx = 0;
	int alter = scanf("%d", &accidx);
	Account* temp = head;
	while (temp!=NULL&&temp->accNum!=accidx)
	{
		temp = temp->next;
	}
	if (temp == NULL || temp->accNum != accidx) {
		printf("抱歉没有找到该账户，请返回主界面！！！\n");
		system("pause");
	}
	else if (temp != NULL && temp->accNum == accidx) {
		printf("请您输入您想要修改的信息！！！(1:卡号，2：余额)\n");
		int opt = 0;
		int opts = scanf("%d", &opt);
		getchar();
		switch (opt)
		{
		case 1:
			printf("请输入修改的卡号......\n");
			scanf("%d",&temp->accNum);
			printf("修改成功！");
			system("pause");
			break;
		case 2: 
			printf("请输入修改的余额......\n");
			scanf("%f",&temp->money);
			printf("修改成功！");
			system("pause");
			break;
		default:
			break;
		}


	}
}
void account_save(int countsize) {
    FILE* p = fopen("save.txt", "w");
    if (!p) {
        printf("打开文件失败！！！\n");
        system("pause");
        fclose(p);
        return;
    }
    for (int i = 0; i < countsize; i++) {
        if (count[i] == 1) {
            fprintf(p, "%d\t%.2f\n", ac[i].accNum, ac[i].money);
        }
    }
    fclose(p);
    printf("保存成功！！！\n");
    system("pause");
}
void menu1() {
	printf("**********************************************\n");
	printf("*----------------《高级选项》------------------*\n");
	printf("*------------1,输出不同银行账户存款金额排序-----------*\n");
	printf("*------------2,输出存款最高账户-----------*\n");
	printf("*------------3,输出存款最低账户-----------*\n");
	printf("*------------4,输出当前资产总和-----------*\n");
	printf("*-------------请输入你的选择！！！-----------*\n");
	printf("*---------------0、退       出---------------*\n");
	printf("**********************************************\n");
}
void account_sort() {	
    Account sttemp[100];
	int idx = 0;
	for (int i = 0; i < 100; i++) {
			sttemp[idx] = ac[i];
			idx += 1;
		}
	printf("%d\n",idx);
	for (int i = 0; i < idx; i++) {
		for (int j = 0; j < idx - 1- i; j++) {
			if (sttemp[j].money < sttemp[j + 1].money) {
				Account temp = sttemp[j];
				sttemp[j] = sttemp[j + 1];
				sttemp[j + 1] = temp;
			}
		}
	}
	printf("*------------       查询成功      -----------*\n");
	printf("卡号\t余额\n");
	for (int i = 0; i < idx; i++) 
	printf("%d\t%.2f\n", sttemp[i].accNum, sttemp[i].money);
}
	

	
void account_max() {
	int idx = 0;
	float num = 0;
	for (int i = 0; i < 100; i++) {
		if (count[i] == 1&&ac[i].money>num) {
			idx = i;
			num = ac[i].money;
		}
		
	}
	printf("*------------       查询成功      -----------*\n");
	printf("卡号\t余额\n");
	printf("%d\t%.2f\n", ac[idx].accNum, ac[idx].money);
}
void account_min() {
	int idx = 0;
	float num = *****************;
	for (int i = 0; i < 100; i++) {
		if (count[i] == 1 && ac[i].money < num) {
			idx = i;
			num = ac[i].money;
		}
	}
	printf("*------------       查询成功      -----------*\n");
	printf("卡号\t余额\n");
	printf("%d\t%.2f\n", ac[idx].accNum, ac[idx].money);
}
void account_sum(){
	float sum=0;
	for(int i=0;i<100;i++){
		if(count[i]==1)
		sum+=ac[i].money;
	}
	printf("-------------       查询成功      ------------*\n");
	printf("%.2f\n",sum);
}   
void othermenu() {
	int n = 1;
	while (n != 0)
	{
		menu1();
		int index = 0;
		scanf("%d", &index);
		switch (index)
		{
		case 1:
			account_sort();
			break;
		case 2:
			account_max();
			break;
		case 3:
			account_min();
			break;
		case 4:
			account_sum();
			break;
		case 0:
			n = 0;
			break;
		default:
			break;
		}
	}
}





