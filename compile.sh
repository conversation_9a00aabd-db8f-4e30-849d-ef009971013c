#!/bin/bash

echo "正在编译俄罗斯方块程序..."

# 检查是否有 g++ 编译器
if ! command -v g++ &> /dev/null; then
    echo "错误: 未找到 g++ 编译器"
    echo "请安装 g++ 编译器:"
    echo ""
    echo "Ubuntu/Debian: sudo apt-get install g++"
    echo "CentOS/RHEL:   sudo yum install gcc-c++"
    echo "macOS:         xcode-select --install"
    echo ""
    exit 1
fi

# 编译程序
g++ -std=c++11 -Wall -Wextra -o tetris "俄罗斯方块.cpp"

if [ $? -eq 0 ]; then
    echo "编译成功! 生成了 tetris"
    echo ""
    echo "按 Enter 键运行程序..."
    read
    ./tetris
else
    echo "编译失败，请检查错误信息"
fi
