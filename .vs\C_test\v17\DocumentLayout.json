{"Version": 1, "WorkspaceRootPath": "F:\\a\\C_test\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|F:\\a\\C_test\\CppProperties.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:CppProperties.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|F:\\a\\C_test\\数据结构课设预约管理系统.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:数据结构课设预约管理系统.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "CppProperties.json", "DocumentMoniker": "F:\\a\\C_test\\CppProperties.json", "RelativeDocumentMoniker": "CppProperties.json", "ToolTip": "F:\\a\\C_test\\CppProperties.json", "RelativeToolTip": "CppProperties.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAYAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-01-17T13:06:57.632Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "数据结构课设预约管理系统.cpp", "DocumentMoniker": "F:\\a\\C_test\\数据结构课设预约管理系统.cpp", "RelativeDocumentMoniker": "数据结构课设预约管理系统.cpp", "ToolTip": "F:\\a\\C_test\\数据结构课设预约管理系统.cpp", "RelativeToolTip": "数据结构课设预约管理系统.cpp", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-01-17T13:06:50.853Z", "EditorCaption": ""}]}]}]}