@echo off
echo 正在编译俄罗斯方块程序...

REM 检查是否有 g++ 编译器
where g++ >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到 g++ 编译器
    echo 请安装 MinGW-w64 或其他 C++ 编译器
    echo.
    echo 推荐安装方法:
    echo 1. 下载并安装 MinGW-w64: https://www.mingw-w64.org/downloads/
    echo 2. 或者安装 Dev-C++: https://sourceforge.net/projects/orwelldevcpp/
    echo 3. 或者安装 Code::Blocks: http://www.codeblocks.org/downloads
    pause
    exit /b 1
)

REM 编译程序
g++ -std=c++11 -Wall -Wextra -static-libgcc -static-libstdc++ -o tetris.exe "俄罗斯方块.cpp"

if %errorlevel% equ 0 (
    echo 编译成功! 生成了 tetris.exe
    echo.
    echo 按任意键运行程序...
    pause >nul
    tetris.exe
) else (
    echo 编译失败，请检查错误信息
    pause
)
