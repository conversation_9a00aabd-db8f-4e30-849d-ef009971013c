@echo off
chcp 65001 >nul
title C++ 编译器安装助手

echo.
echo ╔══════════════════════════════════════════════════╗
echo ║                C++ 编译器安装助手                ║
echo ╚══════════════════════════════════════════════════╝
echo.

echo 正在检查系统中的编译器...
echo.

REM 检查现有编译器
set "FOUND_COMPILER=0"

where g++ >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ 已安装 g++ 编译器
    g++ --version | findstr "g++"
    set "FOUND_COMPILER=1"
)

where gcc >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ 已安装 gcc 编译器
    gcc --version | findstr "gcc"
    set "FOUND_COMPILER=1"
)

where cl >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ 已安装 Visual Studio 编译器 (cl)
    cl 2>&1 | findstr "Microsoft"
    set "FOUND_COMPILER=1"
)

if %FOUND_COMPILER% equ 1 (
    echo.
    echo ✓ 系统中已有可用的编译器！
    echo 您可以直接运行俄罗斯方块游戏了。
    echo.
    echo 按任意键运行游戏...
    pause >nul
    call "直接运行.bat"
    goto :end
)

echo ❌ 未找到任何 C++ 编译器
echo.
echo 请选择安装方式：
echo.
echo 1. 自动下载安装 MinGW-w64 (推荐)
echo 2. 手动安装指导
echo 3. 使用在线编译器
echo 4. 退出
echo.
set /p choice="请输入选择 (1-4): "

if "%choice%"=="1" goto :auto_install
if "%choice%"=="2" goto :manual_guide
if "%choice%"=="3" goto :online_compiler
if "%choice%"=="4" goto :end

:auto_install
echo.
echo 正在尝试自动安装 MinGW-w64...
echo.

REM 检查是否有 winget
where winget >nul 2>&1
if %errorlevel% equ 0 (
    echo 使用 winget 安装 MinGW-w64...
    winget install -e --id=Mingw-w64.Mingw-w64
    if %errorlevel% equ 0 (
        echo ✓ 安装成功！请重启命令提示符或 VS Code
        goto :end
    )
)

REM 检查是否有 chocolatey
where choco >nul 2>&1
if %errorlevel% equ 0 (
    echo 使用 Chocolatey 安装 MinGW-w64...
    choco install mingw -y
    if %errorlevel% equ 0 (
        echo ✓ 安装成功！请重启命令提示符或 VS Code
        goto :end
    )
)

echo 自动安装失败，请选择手动安装。
goto :manual_guide

:manual_guide
echo.
echo ╔══════════════════════════════════════════════════╗
echo ║                  手动安装指导                    ║
echo ╚══════════════════════════════════════════════════╝
echo.
echo 推荐安装方案：
echo.
echo 【方案 1: MinGW-w64 (推荐)】
echo 1. 访问: https://www.mingw-w64.org/downloads/
echo 2. 选择 "MingW-W64-builds"
echo 3. 下载并安装到 C:\mingw64
echo 4. 将 C:\mingw64\bin 添加到系统 PATH
echo.
echo 【方案 2: Visual Studio Community (免费)】
echo 1. 访问: https://visualstudio.microsoft.com/zh-hans/vs/community/
echo 2. 下载并安装
echo 3. 选择 "使用 C++ 的桌面开发" 工作负载
echo.
echo 【方案 3: Dev-C++ (简单易用)】
echo 1. 访问: https://sourceforge.net/projects/orwelldevcpp/
echo 2. 下载并安装 (包含编译器)
echo.
echo 按任意键打开下载页面...
pause >nul
start https://www.mingw-w64.org/downloads/
goto :end

:online_compiler
echo.
echo 正在打开在线编译器页面...
start "在线编译器.html"
goto :end

:end
echo.
echo 安装完成后，请：
echo 1. 重启 VS Code
echo 2. 运行 "直接运行.bat"
echo 3. 或按 F5 在 VS Code 中运行
echo.
pause
