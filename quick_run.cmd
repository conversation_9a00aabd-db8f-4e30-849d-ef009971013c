@echo off
echo Compiling Tetris Game...

REM Try g++
where g++ >nul 2>&1
if %errorlevel% equ 0 (
    echo Using g++ compiler...
    g++ -std=c++11 -static-libgcc -static-libstdc++ -o tetris.exe "俄罗斯方块.cpp"
    if %errorlevel% equ 0 (
        echo Compilation successful!
        echo Starting game...
        tetris.exe
        goto end
    )
)

REM Try cl
where cl >nul 2>&1
if %errorlevel% equ 0 (
    echo Using Visual Studio compiler...
    cl /EHsc /Fe:tetris.exe "俄罗斯方块.cpp"
    if %errorlevel% equ 0 (
        echo Compilation successful!
        echo Starting game...
        tetris.exe
        del *.obj >nul 2>&1
        goto end
    )
)

echo ERROR: No C++ compiler found!
echo Please install MinGW-w64 or Visual Studio
echo Or run: 安装编译器.bat
pause

:end
pause
