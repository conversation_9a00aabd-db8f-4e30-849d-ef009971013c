#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <conio.h>
#include <time.h>

struct Room
{
    char userName[20]; // 姓名
    int state;         // 自习室状态
    clock_t inTime;    // 入住时间
    clock_t outTime;   // 退房时间
} room[5][5];

typedef struct Node
{
    char id[20];   // 学号 
    char name[20]; // 名字 
    char grade[10];  // 年级 
    char tele[50]; // 电话 
    int room;      // 自习室号 
    int days;      // 预约时间
    int num;

    struct Node *next; // 指针域 
} node;

node List = {0}; // 初始化链表头节点

void createFace();
int readFile(node *L); // 读取文件
int saveFile(node *L); // 保存文件
void Home(); // 主菜单界面 
void swit(int x); // 功能选择 
int Int(); // 办理入住
void insertLink(node *L, node e); // 插入预约人信息 
void Out(node *L); // 办理退房 
void deletLink(node *L, node *pr); // 删除预约人信息 
void Change(node *L); // 修改预约信息
node * Inquire(node *L); // 查询预约信息
node * searchNumber(char id[], node *L); // 按学号进行查找  
node * searchName(char name[], node *L); // 按名字进行查找 
node * searchRoom(int room, node *L); // 按自习室号进行查找
void Message(node *L); // 输出预约信息
void Exit(); // 退出程序
int Sign(); // 登录程序
void createFace(node *L); // 结构体数组初始化 
void roomarray_Init(node *L); // 结构体数组初始化

int main() // 主函数 
{
    Sign(); 
    readFile(&List);

    while(1)
    {
        Home(); 
        int choice;
        scanf("%d", &choice);
        if (choice == 5) break; // 退出程序
        swit(choice);
        printf("-----------按任意键继续-------------");
        _getch(); 
    }
    Exit();
}

int readFile(node *L) // 读取文件 
{
    FILE *fpr = fopen("b.txt", "r");
    node st, *s, *t = L;

    if (fpr == NULL)
    {
        printf("文件打开失败。\n");
        return 0;
    }
    while (fscanf(fpr, "%s %s %s %s %d %d %d", st.id, st.name, st.grade, st.tele, &st.room, &st.days, &st.num) != EOF)
    {
        s = (node *)malloc(sizeof(node));
        if (!s) {
            printf("内存分配失败。\n");
            fclose(fpr);
            return 0;
        }
        *s = st;
        t->next = s;
        t = s;
        t->next = NULL;
    }
    fclose(fpr);
    roomarray_Init(L); // 初始化自习室状态
    return 1;
}

int saveFile(node *L) // 保存文件
{
    FILE *fpw = fopen("b.txt", "w"); 
    if (fpw == NULL)
    {
        printf("文件打开失败。\n");
        return 0;
    }
    node *p = L->next;
    while (p != NULL)
    {
        fprintf(fpw, "%s %s %s %s %d %d %d\n", p->id, p->name, p->grade, p->tele, p->room, p->days, p->num);
        p = p->next;
    }
    fclose(fpw);
    return 1;
}

void swit(int x) // 功能选择 
{
    switch(x)
    {
        case 1: Int(); break;
        case 2: Out(&List); break;
        case 3: Change(&List); break;
        case 4: Inquire(&List); break;
        default: printf("无效的选择，请重新输入。\n"); break;
    }
}

void Home() // 登录选择界面 
{
    system("cls");
    printf("----------------------------\n");
    printf("---     自习室预约管理系统     ---\n");
    printf("----------------------------\n");
    Message(&List);
    printf("---     办理预约信息 ---1       ---\n");
    printf("---     办理结束学习 ---2       ---\n");
    printf("---     修改预约信息 ---3       ---\n");
    printf("---     查询同学信息 ---4       ---\n");
    printf("---     退出程序     ---5       ---\n"); 
    printf("---     请输入对应功能数字：");
}

int Int() // 办理入住 
{   
    int num = 0, floor = 0, roomNum = 0;
    node st;
    system("cls");
    createFace();
    printf("请输入下列信息以方便预约登记\n");

    printf("自习室号："); 
    scanf("%d", &num);
    floor = (num / 10) - 1;
    roomNum = (num % 10);
    
    if (room[floor][roomNum].state != 0)
    {
        printf("对不起，该自习室已有同学预约，请重新选择\n");
        return 0;
    }
    printf("请再次确认自习室号："); 
    scanf("%d", &st.room);
    room[floor][roomNum].state = 1;
    printf("名字："); 
    scanf("%s", st.name);
    strcpy(room[floor][roomNum].userName, st.name);
    printf("学号：");
    scanf("%s", st.id);
    printf("年级："); 
    scanf("%s", st.grade);
    printf("电话："); 
    scanf("%s", st.tele);

    printf("预约时间："); 
    scanf("%d", &st.days);
    st.num = st.days * 100;
    insertLink(&List, st);
    return 1;
}

void Change(node *L) // 修改预约信息
{
    int num0 = 0, num2 = 0, floor = 0, roomNum = 0;
    system("cls");
    int choice = 0;
    node *st = Inquire(L);
    while (st != NULL)
    {
        printf("修改学号 --- 1\n");
        printf("修改姓名     --- 2\n");
        printf("修改年级     --- 3\n");
        printf("修改电话     --- 4\n");
        printf("修改自习室号   --- 5\n");
        printf("修改预约时间 --- 6\n");       
        printf("请输入要修改的信息：");
        scanf("%d", &choice);
        switch (choice)
        {
            case 1:
                printf("\n请输入新的学号："); 
                scanf("%s", st->id);
                break;
            case 2:
                printf("\n请输入新的姓名："); 
                scanf("%s", st->name);
                break;
            case 3:
                printf("\n请输入新的年级："); 
                scanf("%s", st->grade);
                break;
            case 4:
                printf("\n请输入新的电话："); 
                scanf("%s", st->tele);
                break;
            case 5:
                num0 = st->room;
                floor = (num0 / 10) - 1;
                roomNum = (num0 % 10) ;
                room[floor][roomNum].state = 0;
                printf("\n请输入新的自习室号："); 
                scanf("%d", &st->room);
                num2 = st->room;
                floor = (num2 / 10) - 1;
                roomNum = (num2 % 10) ;
                room[floor][roomNum].state = 1;
                break;
            case 6:
                printf("\n请输入新的预约日期："); 
                scanf("%d", &st->days);
                break;
            default:
                printf("无效的选择，请重新输入。\n");
                continue;
        }
        
        st->num = st->days * 100;
        
        printf("|学号|姓名\t|年级\t|电话\t|自习室号\t|预约时间|\n");
        printf("%s\t  %s\t  %s\t  %s\t  0%d\t  %d\n", st->id, st->name, st->grade, st->tele, st->room, st->days);   
        printf("是否继续修改信息（yes - 1/no - 0）："); 
        scanf("%d", &choice);
        
        if (choice == 0)
        {
            break;
        }
    }
    saveFile(L); 
}

void insertLink(node *L, node e) // 插入预约人信息 
{
    node *newNode = (node *)malloc(sizeof(node));
    if (!newNode) {
        printf("内存分配失败。\n");
        return;
    }
    *newNode = e;
    newNode->next = L->next;
    L->next = newNode;
}

void Out(node *L) // 办理退房 
{
    int num = 0, floor = 0, roomNum = 0;
    system("cls");
    int room1, choice;
    node *p, *st = NULL;

    printf("请输入要查询的自习室号：");
    scanf("%d", &room1);
    num = room1;
    floor = (num / 10) - 1;
    roomNum = (num % 10) ;
    st = searchRoom(room1, L);

    if (st == NULL)
    {
        printf("查无自习室!\n");
        return;
    }
    else
    {
        printf("___________________________________________________________________\n");
        printf("|学号|姓名\t|年级\t|电话\t|自习室号\t|预约时间\n");
        printf("___________________________________________________________________\n");
        printf("%s\t  %s\t  %s\t  %s\t  0%d\t  %d\n", st->id, st->name, st->grade, st->tele, st->room, st->days);   
        printf("___________________________________________________________________\n");
    }

    printf("欢迎 %s 下次光临\n", st->name); 

    deletLink(L, st);
    room[floor][roomNum].state = 0;
    strcpy(room[floor][roomNum].userName, "NULL");
    saveFile(L);
}

void deletLink(node *L, node *pr) // 删除预约人信息 
{
    if (L == NULL || pr == NULL) return;
    node *s = L;
    while (s->next != pr) {
        s = s->next;
    }
    s->next = pr->next;
    free(pr);
}

node * Inquire(node *L) // 查询预约信息
{
    system("cls");
    int choice;
    char id[20];
    char name[50]; 
    int room;
    node *st;

    printf("请选择一种方式\n");
    printf("学号--1\n");
    printf("姓名------2\n"); 
    printf("自习室号----3\n"); 
    printf("请输入方式：");
    scanf("%d", &choice);
    if (choice == 1)
    {
        printf("请输入要查询的学号：");
        scanf("%s", id);
        st = searchNumber(id, L); 

        if (st == NULL)
        {
            printf("查无此人!\n");
            return NULL;
        }
        else
        {
            printf("___________________________________________________________________\n");
            printf("|学号|姓名\t|年级\t|电话\t|自习室号\t|预约时间|\n");
            printf("___________________________________________________________________\n");
            printf("%s\t  %s\t  %s\t  %s\t  0%d\t  %d\n", st->id, st->name, st->grade, st->tele, st->room, st->days);   
            printf("___________________________________________________________________\n");
        }
    }
    else if (choice == 2)
    {   
        printf("请输入要查询的姓名：");
        scanf("%s", name);
        st = searchName(name, L); 

        if (st == NULL)
        {
            printf("查无此人!\n");
            return NULL;
        }
        else
        {
            printf("___________________________________________________________________\n");
            printf("|学号|姓名\t|年级\t|电话\t|自习室号\t|预约时间|\n");
            printf("___________________________________________________________________\n");
            printf("%s\t  %s\t  %s\t  %s\t  0%d\t  %d\n", st->id, st->name, st->grade, st->tele, st->room, st->days);   
            printf("___________________________________________________________________\n");
        }
    }
    else if (choice == 3)
    {
        printf("请输入要操作的自习室号：");
        scanf("%d", &room);
        st = searchRoom(room, L); 

        if (st == NULL)
        {
            printf("查无此人!\n");
            return NULL;
        }
        else
        {
            printf("___________________________________________________________________\n");
            printf("|学号|姓名\t|年级\t|电话\t|自习室号\t|预约时间|\n");
            printf("___________________________________________________________________\n");
            printf("%s\t  %s\t  %s\t  %s\t  0%d\t  %d\n", st->id, st->name, st->grade, st->tele, st->room, st->days);   
            printf("___________________________________________________________________\n");
        }
    }
    return st;
}

node * searchNumber(char id[], node *L) // 按学号进行查找 
{
    node *p = L->next;
    while (p != NULL)
    {
        if (strcmp(id, p->id) == 0)
        {
            return p;
        }
        p = p->next;
    }
    return NULL;
}

node * searchName(char name[], node *L) // 按名字进行查找 
{
    node *p = L->next;
    while (p != NULL)
    {
        if (strcmp(name, p->name) == 0)
        {
            return p;
        }
        p = p->next;
    }
    return NULL;
}

node * searchRoom(int room, node *L) // 按自习室号进行查找
{
    node *p = L->next;
    while (p != NULL)
    {
        if (p->room == room)
        {
            return p;
        }
        p = p->next;
    } 
    return NULL;
}

void Message(node *L) // 输出预约信息 
{   
    roomarray_Init(L);
    createFace();
    node *p = L->next;
    printf("___________________________________________________________________\n");
    printf("|学号|姓名\t|年级\t|电话\t|自习室号\t|预约时间|\n");
    printf("___________________________________________________________________\n");
    while (p != NULL)
    {
        printf("%s\t  %s\t  %s\t  %s\t  0%d\t       %d\n", p->id, p->name, p->grade, p->tele, p->room, p->days);
        printf("___________________________________________________________________\n");
        p = p->next;
    }
}

int Sign() // 登录程序
{
    char c;
    char passw[] = "1111";
    char test[20] = {0};
    int count = 0; // 用来记录登录次数

    while (count < 3)
    {
        printf("-------登录系统---------\n");
        printf("请输入系统密码:");
        int i = 0; // 记录密码长度
        while (1) 
        {
            c = _getch(); // 用 _getch() 函数输入，字符不会显示在屏幕上
            if (c == '\r') 
            { // 遇到回车，表明密码输入结束
                break; // while 循环的出口
            }
            else if (c == '\b') 
            { // 遇到退格，需要删除前一个星号
                printf("\b \b");  // 退格，打一个空格，再退格，实质上是用空格覆盖掉星号
                --i;
            }
            else {
                test[i++] = c; // 将字符放入数组
                printf("*");     
            }
        }
        if (strcmp(passw, test) == 0)
        {
            printf("\n登陆成功\n");
            break;
        }
        else
        {
            printf("密码错误\n");
            count++;
        }
    }
    if (count == 3)
    {
        printf("退出程序\n");
        Exit();
    }
    return 0;
} 

void Exit() // 退出程序
{
    system("cls");
    printf("欢迎下次使用~\n");
    exit(0); 
}

void createFace() 
{   
    system("cls");
    printf("自习室状态：\n");
    for (int i = 0; i < 5; i++)
    {
        for (int j = 0; j < 5; j++)
        {
            printf("%d%d\t", i + 1, j );
            if (room[i][j].state == 0) // 判断当前自习室是否已入住
            {
                printf("空闲\t");
            }
            else
            {
                printf("已入住\t");
            }
        }
        printf("\n");
    }
}

/* 初始化时，遍历链表，使文件读取的值赋给结构体数组，完成初始化*/ 
void roomarray_Init(node *L) // 结构体数组初始化 roomarray_Init(&List) 
{   
    node *p = L->next;
    if (p != NULL)
    {
        for (int i = 0; i < 5; i++)
        {
            for (int j = 0; j < 5; j++)
            {
                while (p != NULL)
                {
                    int num = p->room;
                    int floor = (num / 10) - 1;
                    int roomNum = (num % 10) ;
                    if (i == floor && j == roomNum)
                    {
                        strcpy(room[i][j].userName, p->name);
                        room[i][j].state = 1;
                        break;
                    }
                    p = p->next;
                }
            }
        }
    }
}