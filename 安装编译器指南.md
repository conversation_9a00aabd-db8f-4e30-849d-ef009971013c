# 🔧 C++ 编译器安装指南

## 问题诊断

您遇到的错误 `"MIDDebuggerPath" option` 表明：
1. VS Code 找不到调试器 (gdb.exe)
2. 系统中没有安装 C++ 编译器

## 🚀 推荐解决方案

### 方案 1: 安装 MinGW-w64 (推荐)

**步骤 1: 下载安装器**
- 访问: https://www.mingw-w64.org/downloads/
- 选择 "MingW-W64-builds"
- 下载最新版本的安装器

**步骤 2: 安装**
1. 运行下载的安装器
2. 选择以下配置：
   - Version: 最新版本
   - Architecture: x86_64
   - Threads: posix
   - Exception: seh
   - Build revision: 最新
3. 安装路径建议: `C:\mingw64`

**步骤 3: 配置环境变量**
1. 右键 "此电脑" → "属性"
2. 点击 "高级系统设置"
3. 点击 "环境变量"
4. 在 "系统变量" 中找到 "Path"
5. 点击 "编辑" → "新建"
6. 添加: `C:\mingw64\bin`
7. 点击 "确定" 保存

**步骤 4: 验证安装**
打开命令提示符，输入：
```cmd
g++ --version
gdb --version
```

### 方案 2: 使用 MSYS2 (更简单)

**步骤 1: 下载 MSYS2**
- 访问: https://www.msys2.org/
- 下载并安装 MSYS2

**步骤 2: 安装编译器**
打开 MSYS2 终端，运行：
```bash
pacman -S mingw-w64-x86_64-gcc
pacman -S mingw-w64-x86_64-gdb
```

**步骤 3: 添加到 PATH**
将 `C:\msys64\mingw64\bin` 添加到系统 PATH

### 方案 3: Visual Studio Community (免费)

**步骤 1: 下载**
- 访问: https://visualstudio.microsoft.com/zh-hans/vs/community/
- 下载 Visual Studio Community

**步骤 2: 安装**
1. 运行安装器
2. 选择 "使用 C++ 的桌面开发" 工作负载
3. 确保勾选 "MSVC v143 编译器工具集"
4. 安装

## 🎮 快速运行游戏

安装编译器后，有以下几种运行方式：

### 方式 1: 使用批处理文件 (最简单)
双击运行 `run_tetris.bat`

### 方式 2: 手动编译
打开命令提示符，运行：
```cmd
g++ -std=c++11 -Wall -Wextra -static-libgcc -static-libstdc++ -o "俄罗斯方块.exe" "俄罗斯方块.cpp"
"俄罗斯方块.exe"
```

### 方式 3: 在 VS Code 中运行
1. 打开 `俄罗斯方块.cpp`
2. 按 `Ctrl+F5` (运行而不调试)
3. 或按 `F5` (调试运行)

## 🔍 故障排除

### 问题 1: "g++ 不是内部或外部命令"
**解决方案**: 检查环境变量 PATH 是否包含编译器路径

### 问题 2: "找不到 gdb"
**解决方案**: 确保安装了完整的 MinGW-w64 工具链

### 问题 3: 中文字符显示问题
**解决方案**: 
1. 在命令提示符中运行: `chcp 65001`
2. 或使用 Windows Terminal

### 问题 4: VS Code 调试器错误
**解决方案**: 
1. 确保安装了 C/C++ 扩展
2. 检查 `.vscode/launch.json` 中的 `miDebuggerPath` 设置

## 📝 验证安装

运行以下命令验证安装：
```cmd
g++ --version
gdb --version
where g++
where gdb
```

如果都能正常显示版本信息，说明安装成功！

## 🎯 下一步

1. 安装编译器
2. 验证安装
3. 运行 `run_tetris.bat`
4. 享受游戏！

如果仍有问题，请提供具体的错误信息。
