#include <windows.h>
#include <ctime>
#include <conio.h>
#include <cstdio>
#include <math.h>
#include <cstdlib>

// 禁用弃用函数警告
#pragma warning(disable: 4996)

// 函数声明
void set_difficulty(int level);
void drawBlock(int x, int y, int color);
void clearBlock(int x, int y);
void hideCursor();

// 游戏速度和难度设置
#define Speed 600
#define EASY_SPEED 800
#define NORMAL_SPEED 600
#define HARD_SPEED 400

// 游戏状态枚举
enum GameState {
    MENU,
    PLAYING,
    PAUSED,
    GAME_OVER
};

// 全局变量
int board[30][30][2];
int sort[7] = { 0, 1, 3, 5, 7, 11, 15 };
int mark = 0;
int amend_x = 10, amend_y = 4, speed = Speed;
time_t seed;
int kind = 0, next_kind;
int key;
int win = 1;
int pause = 0;
int difficulty = 1;
char difficulty_names[3][10] = { "Easy", "Normal", "Hard" };
enum GameState currentState = MENU;
int selectedDifficulty = 1;

// 方块动态结构体
struct now {
    int x[4];
    int y[4];
    int color;
} now[19] = {
    {{0, 2, 0, 2}, {-1, -1, -2, -2}, 10}, //方块系 num:0
    {{0, 6, 4, 2}, {-1, -1, -1, -1}, 11}, {{2, 2, 2, 2}, {-1, -2, -3, -4}, 11}, //长条系 num:1-2 
    {{0, 4, 2, 2}, {-2, -1, -1, -2}, 12}, {{0, 2, 0, 2}, {-1, -2, -2, -3}, 12}, //  L系 num:3-4;
    {{0, 4, 2, 2}, {-1, -2, -1, -2}, 13}, {{0, 2, 2, 0}, {-2, -1, -2, -3}, 13}, //反L 系 num:5-6;
    {{0, 4, 2, 2}, {-1, -1, -1, -2}, 14}, {{0, 2, 0, 0}, {-1, -2, -2, -3}, 14}, {{0, 4, 2, 2}, {-2, -2, -1, -2}, 14}, {{0, 2, 2, 2}, {-2, -1, -2, -3}, 14}, //T方块系 num:7-10
    {{0, 4, 2, 0}, {-1, -1, -1, -2}, 9}, {{0, 2, 0, 0}, {-1, -3, -2, -3}, 9}, {{0, 4, 2, 4}, {-2, -1, -2, -2}, 9}, {{0, 2, 2, 2}, {-1, -1, -2, -3}, 9}, //Z方块系 num:11-14
    {{0, 4, 2, 4}, {-1, -1, -1, -2}, 5}, {{0, 2, 0, 0}, {-1, -1, -2, -3}, 5}, {{0, 4, 2, 0}, {-1, -2, -2, -2}, 5}, {{0, 2, 2, 2}, {-3, -1, -2, -3}, 5}  //S方块系 num:15-18
};

// 光标和颜色设置函数
void gotoxy(int x, int y) {
    COORD pos = { static_cast<SHORT>(x), static_cast<SHORT>(y) };
    SetConsoleCursorPosition(GetStdHandle(STD_OUTPUT_HANDLE), pos);
}

void color(int b) {
    SetConsoleTextAttribute(GetStdHandle(STD_OUTPUT_HANDLE), b);
}

// 隐藏光标
void hideCursor() {
    CONSOLE_CURSOR_INFO cursorInfo;
    GetConsoleCursorInfo(GetStdHandle(STD_OUTPUT_HANDLE), &cursorInfo);
    cursorInfo.bVisible = FALSE;
    SetConsoleCursorInfo(GetStdHandle(STD_OUTPUT_HANDLE), &cursorInfo);
}

// 绘制方块（只用一个“█”字符，x坐标*2）
void drawBlock(int x, int y, int color) {
    gotoxy(x * 2, y);
    SetConsoleTextAttribute(GetStdHandle(STD_OUTPUT_HANDLE), color);
    printf("█");
    SetConsoleTextAttribute(GetStdHandle(STD_OUTPUT_HANDLE), 7);
}

// 清除方块
void clearBlock(int x, int y) {
    gotoxy(x * 2, y);
    printf(" ");
}

// 显示主菜单
void showMainMenu() {
    system("cls");
    color(15); // 白色
    gotoxy(30, 5);
    printf("=== 俄罗斯方块 ===");

    color(14); // 黄色
    gotoxy(28, 8);
    printf("请选择难度:");

    for (int i = 0; i < 3; i++) {
        if (i + 1 == selectedDifficulty) {
            color(12); // 红色
            gotoxy(28, 10 + i);
            printf("-> %s", difficulty_names[i]);
        }
        else {
            color(15); // 白色
            gotoxy(30, 10 + i);
            printf("%s", difficulty_names[i]);
        }
    }

    color(10); // 绿色
    gotoxy(28, 14);
    printf("按 Enter 开始游戏");
    gotoxy(28, 15);
    printf("按 ESC 退出游戏");
}

// 处理菜单输入
void handleMenuInput() {
    int key = getch();
    if (key == 224) { // 方向键
        key = getch();
        if (key == 80 && selectedDifficulty < 3) { // 下箭头
            selectedDifficulty++;
        }
        else if (key == 72 && selectedDifficulty > 1) { // 上箭头
            selectedDifficulty--;
        }
        showMainMenu();
    }
    else if (key == 13) { // Enter
        difficulty = selectedDifficulty;
        currentState = PLAYING;
        system("cls");
    }
    else if (key == 27) { // ESC
        exit(0);
    }
}

// 显示游戏边界
void boundary() {
    int i = 0, j = 0;
    gotoxy(2, 1);
    for (i = 0; i < 26; i++) {
        printf("█");
    }
    gotoxy(2, 27);
    for (i = 0; i < 26; i++) {
        printf("█");
    }
    for (i = 0; i < 25; i++) {
        gotoxy(2, 2 + i);
        printf("█");
        gotoxy(34, 2 + i);
        if (i == 7 || i == 13) {
            printf("████");
        }
        else {
            printf("█");
        }
        gotoxy(52, 2 + i);
        printf("█");
    }

    // 显示难度信息
    color(7);
    gotoxy(37, 9);
    printf("Difficulty: %s", difficulty_names[difficulty - 1]);

    int t = 100;
    color(7);
    gotoxy(37, 11);
    printf("Highest: %d", t);
    color(7);
    gotoxy(37, 13);
    printf("Now: %d", mark);
    gotoxy(37, 17);
    printf("↑:Rotate");
    gotoxy(37, 19);
    printf("↓:Expedite");
    gotoxy(37, 21);
    printf("←:Left ");
    gotoxy(37, 23);
    printf("→:Right");
    gotoxy(37, 25);
    printf("Space:pause");
    gotoxy(37, 27);
    printf("P:Change Difficulty (1-3)");
    color(12);
    gotoxy(3, 29);
    gotoxy(11, 30);
    printf("Tetris (Recast In 1893.7.26)");
    color(7);
    for (int i = 0; i < 30; ++i) {
        board[26][i][0] = 1;
    }
}

// 显示下一个方块
void next() {
    srand(static_cast<unsigned int>(time(&seed)));
    next_kind = sort[rand() % 7];
    gotoxy(37, 3);
    printf("Next One");
    for (int i = 0; i < 4; ++i) {
        clearBlock(now[kind].x[i] + 18, now[kind].y[i] + 7);
    }
    for (int i = 0; i < 4; ++i) {
        drawBlock(now[next_kind].x[i] + 18, now[next_kind].y[i] + 7, now[next_kind].color);
    }
}

// 左右移动碰撞判断
void left_and_rigth(int dir) {
    if (now[kind].x[0] + amend_x >= 6 && now[kind].x[1] + amend_x <= 30) {
        if (dir == 3) {
            for (int i = 0; i < 4; ++i) {
                int x = now[kind].x[i] + amend_x;
                int y = now[kind].y[i] + amend_y;
                if (board[y - 1][x / 2 - 3][0] == 1) {
                    key = 0;
                }
            }
        }
        if (dir == 4 || dir == 1) {
            for (int i = 0; i < 4; ++i) {
                int x = now[kind].x[i] + amend_x;
                int y = now[kind].y[i] + amend_y;
                if (board[y - 1][x / 2 - 1][0] == 1) {
                    key = 0;
                }
            }
        }
    }
    if (now[kind].x[0] + amend_x < 6 && dir == 3) {
        key = 0;
    }
    if (now[kind].x[1] + amend_x > 30 && (dir == 4 || dir == 1)) {
        key = 0;
    }
}

// 游戏结束处理
void game_over() {
    for (int i = 25; i >= 0; i--) {
        for (int j = 0; j < 15; j++) {
            drawBlock(16 - j, 2 + i, 7);
            Sleep(10);
            board[i][j][0] = 0;
        }
    }
    for (int j = 0; j < 25; j++) {
        for (int i = 0; i < 15; i++) {
            clearBlock(4 + i, j + 2);
            Sleep(10);
        }
    }
    gotoxy(37, 13);
    printf("Now:%d  ", mark);
    mark = 0;
}

// 输入处理
int input() {
    int ch1 = 0;
    int ch2 = 0;
    if ((ch1 = getch()) != 0) {
        if (ch1 == 224) { // 方向键前缀
            ch2 = getch();
            switch (ch2) {
            case 72: return 1;  // 上
            case 80: return 2;  // 下
            case 75: return 3;  // 左
            case 77: return 4;  // 右
            }
        }
        else {
            // 处理暂停键(Space)和难度切换键(P)
            if (ch1 == 32) { // Space键
                return 5; // 暂停/恢复
            }
            if (ch1 == 'p' || ch1 == 'P') {
                return 6; // 切换难度
            }
        }
    }
    return 0;
}

// 暂停功能实现
void pause_game() {
    while (pause == 1) {
        gotoxy(37, 15);
        color(12);
        printf("Game Paused!");
        gotoxy(37, 17);
        printf("Press Space to resume...");

        if (kbhit()) {
            int key = getch();
            if (key == 32) { // Space键恢复游戏
                pause = 0;
                gotoxy(37, 15);
                printf("                    ");
                gotoxy(37, 17);
                printf("                    ");
                break;
            }
        }
        Sleep(100);
    }
}

// 方块移动
void move(int fall) {
    for (int i = 0; i < 2; ++i) {
        for (int j = 0; j < 4; ++j) {
            int x = now[kind].x[j] + amend_x;
            int y = now[kind].y[j] + amend_y;
            if (i == 0 && y > 1) {
                drawBlock(x, y, now[kind].color);
            }
            if (i == 1 && fall == 1 && y > 1) {
                clearBlock(x, y);
            }
        }
        Sleep(speed * (1 - i));
    }
    gotoxy(37, 13);
    printf("Now:%d  ", mark);
    hideCursor();
}

// 垂直碰撞判断
bool fall_or_not() {
    for (int i = 0; i < 4; ++i) {
        int x = now[kind].x[i] + amend_x;
        int y = now[kind].y[i] + amend_y;
        if (board[y - 1][x / 2 - 2][0] == 1) {
            return false;
        }
    }
    return true;
}

// 方块旋转
void rotate() {
    if (kind > 0 && kind < 3) {
        kind == 1 ? kind = 2 : kind = 1;
    }
    if (kind > 2 && kind < 5) {
        kind == 3 ? kind = 4 : kind = 3;
    }
    if (kind > 4 && kind < 7) {
        kind == 5 ? kind = 6 : kind = 5;
    }
    if (kind > 6 && kind < 11) {
        kind == 10 ? kind = 7 : kind++;
    }
    if (kind > 10 && kind < 15) {
        kind == 14 ? kind = 11 : kind++;
    }
    if (kind > 14) {
        kind == 18 ? kind = 15 : kind++;
    }
}

// 行消除
void line_clear(int n) {
    for (n; n > 1; --n) {
        for (int i = 0; i < 15; ++i) {
            clearBlock(4 + i, n + 1);
            board[n][i][0] = board[n - 1][i][0];
            board[n][i][1] = board[n - 1][i][1];
            if (board[n][i][0] == 1) {
                drawBlock(4 + i, n + 1, board[n][i][1]);
            }
        }
    }
}

// 判断是否满行并处理
void full_or_not() {
    int line = 0;
    for (int i = 1; i < 26; ++i) {
        int k = 0;
        for (int j = 0; j < 15; ++j) {
            if (board[i][j][0] == 1) {
                ++k;
            }
        }
        if (k == 15) {
            ++line;
            line_clear(i);
        }
    }

    // 根据消除行数加分并根据分数调整难度
    if (line > 0) {
        mark += static_cast<int>(pow(3, line - 1));

        // 每得1000分增加难度
        if (mark / 1000 + 1 > difficulty) {
            set_difficulty(mark / 1000 + 1);
        }

        color(7);
        for (int i = 0; i < line * 2; ++i) {
            gotoxy(5, 2);
            i % 2 == 0 ? color(7) : color(12);
            if (line == 1) {
                printf("Cheer for our Britons!");
            }
            if (line == 2) {
                printf("Live long my Queen Victoria!");
            }
            if (line == 3) {
                printf("Cheer for the Golden Age!");
            }
            if (line == 4) {
                printf("God bless Britain Empire!");
            }
            Sleep(100);
            if (i % 2 == 1) {
                Sleep(2000);
            }
        }
        gotoxy(5, 2);
        printf("                             ");
        color(7);
    }
}

// 设置难度等级
void set_difficulty(int level) {
    if (level < 1) level = 1;
    if (level > 3) level = 3;
    difficulty = level;

    // 更新难度显示
    gotoxy(37, 9);
    color(7);
    printf("Difficulty: %s", difficulty_names[difficulty - 1]);

    // 根据难度调整速度
    switch (difficulty) {
    case 1: speed = EASY_SPEED; break;
    case 2: speed = NORMAL_SPEED; break;
    case 3: speed = HARD_SPEED; break;
    }
}

// 游戏主循环
void game() {
    int fall = 1, dir, time = 2, stop = 0;
    amend_x = 10, amend_y = 3;
    if (kind == 1) {
        --amend_y;
    }
    while (fall == 1) {
        // 检查是否需要暂停
        if (pause == 1) {
            pause_game();
        }

        dir = 0;
        key = 1;
        if (kbhit()) {
            dir = input();
            // 处理暂停和难度切换
            if (dir == 5) { // 暂停/恢复
                pause = !pause;
                continue;
            }
            if (dir == 6) { // 切换难度
                set_difficulty(difficulty % 3 + 1);
                continue;
            }

            left_and_rigth(dir);
            if (dir == 1 && key == 1 && amend_y > 4 && stop == 0) {
                rotate();
                time = 2;
            }
            if (dir == 2) {
                speed = 30;
            }
            if (dir == 3 && key == 1) {
                amend_x -= 2;
                if (stop == 0) {
                    --amend_y;
                }
                speed = 30;
                time = 2;
            }
            if (dir == 4 && key == 1) {
                amend_x += 2;
                if (stop == 0) {
                    --amend_y;
                }
                speed = 30;
                time = 2;
            }
        }
        ++amend_y;
        if (fall_or_not() == false) {
            stop = 1;
            for (int i = 0; i < 4; ++i) {
                if (now[kind].y[i] + amend_y < 4) {
                    win = 0;
                }
            }
        }
        if (fall_or_not() == true) {
            stop = 0;
            time = 2;
        }
        if (stop == 1 && time > 0) {
            --time;
            --amend_y;
        }
        if (time == 0) {
            fall = 0;
        }
        move(fall);
        speed = Speed; // 恢复为默认速度，根据难度设置
    }
    for (int i = 0; i < 4; ++i) {
        int x = now[kind].x[i] + amend_x;
        int y = now[kind].y[i] + amend_y;
        board[y - 1][x / 2 - 2][0] = 1;
        board[y - 1][x / 2 - 2][1] = now[kind].color;
    }
    full_or_not();
}

int main() {
    srand(static_cast<unsigned int>(time(&seed)));

    // 隐藏光标
    hideCursor();

    // 主循环
    while (1) {
        switch (currentState) {
        case MENU:
            showMainMenu();
            while (currentState == MENU) {
                if (kbhit()) {
                    handleMenuInput();
                }
            }
            // 初始化游戏
            system("cls");
            boundary();
            set_difficulty(difficulty);
            kind = sort[rand() % 7];
            break;

        case PLAYING:
            // 游戏主循环
            while (currentState == PLAYING && win == 1) {
                next();
                game();
                kind = next_kind;
            }
            if (win == 0) {
                currentState = GAME_OVER;
            }
            break;

        case PAUSED:
            pause_game();
            currentState = PLAYING;
            break;

        case GAME_OVER:
            game_over();
            gotoxy(10, 15);
            color(12);
            printf("游戏结束! 得分: %d", mark);
            gotoxy(10, 17);
            color(15);
            printf("按任意键返回菜单...");
            getch();
            currentState = MENU;
            mark = 0;
            win = 1;
            break;
        }
    }

    return 0;
}