#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <conio.h>
#include <time.h>

struct Room
{
    char userName[20]; // 姓名
    int state;         // 自习室状态
    clock_t inTime;    // 入住时间
    clock_t outTime;   // 退房时间
} room[5][5];

typedef struct Node
{
    char id[20];   // 学号 
    char name[20]; // 名字 
    char grade[10];  // 年级 
    char tele[50]; // 电话 
    int room;      // 自习室号 
    int days;      // 预约时间
    int num;

    struct Node *next; // 指针域 
} node;

node List = {0}; // 初始化链表头节点

void statusrooms();
int readFile(node *L); // 读取文件
int saveFile(node *L); // 保存文件
void Home(); // 主菜单界面 
void swit(int x); // 功能选择 
int Int(); // 办理入住
void insertLink(node *L, node e); // 插入预约人信息 
void Out(node *L); // 办理退房 
void deletLink(node *L, node *pr); // 删除预约人信息 
void Change(node *L); // 修改预约信息
node * Inquire(node *L); // 查询预约信息
node * queryNumber(char id[], node *L); // 按学号进行查找  
node * queryName(char name[], node *L); // 按名字进行查找 
node * queryRoom(int room, node *L); // 按自习室号进行查找
void Message(node *L); // 输出预约信息
void Exit(); // 退出程序
int Sign(); // 登录程序
void statusrooms(node *L); // 结构体数组初始化 
void roomarray_Init(node *L); // 结构体数组初始化

int main() // 主函数 
{
    Sign(); 
    readFile(&List);

    while(1)
    {
        Home(); 
        int choice;
        scanf("%d", &choice);
        if (choice == 5) break; // 退出程序
        swit(choice);
        printf("-----------按任意键继续-------------");
        _getch(); 
    }
    Exit();
}

/**
 * 读取文件函数，用于将文件中的数据读入到链表中
 * @param L 链表的头指针，用于将读取的数据追加到链表中
 * @return 返回0表示读取失败，返回1表示读取成功
 */
int readFile(node *L) 
{
    // 打开文件进行读取
    FILE *fpr = fopen("b.txt", "r");
    node st, *s, *t = L;

    // 检查文件是否成功打开
    if (fpr == NULL)
    {
        printf("文件打开失败。\n");
        return 0;
    }

    // 读取文件中的数据，直到文件结束
    while (fscanf(fpr, "%s %s %s %s %d %d %d", st.id, st.name, st.grade, st.tele, &st.room, &st.days, &st.num) != EOF)
    {
        // 动态分配内存用于存储读取的数据
        s = (node *)malloc(sizeof(node));
        if (!s) {
            printf("内存分配失败。\n");
            fclose(fpr);
            return 0;
        }

        // 将读取的数据复制到新分配的节点中，并将其添加到链表中
        *s = st;
        t->next = s;
        t = s;
        t->next = NULL;
    }

    // 关闭文件
    fclose(fpr);

    // 初始化自习室状态
    roomarray_Init(L); 
    return 1;
}

/**
 * 保存链表数据到文件
 * @param L 链表的头节点指针
 * @return 成功保存返回1，否则返回0
 *
 * 此函数负责将链表中的数据保存到名为b.txt的文件中每行一个节点数据，包括ID、姓名、成绩、电话、房间号、天数和数量
 * 如果文件打开失败，将打印错误信息并返回0
 */
int saveFile(node *L)
{
    // 打开文件以写入模式
    FILE *fpw = fopen("b.txt", "w"); 
    // 检查文件是否成功打开
    if (fpw == NULL)
    {
        printf("文件打开失败。\n");
        return 0;
    }
    
    // 初始化遍历指针p，从链表的第一个有效节点开始
    node *p = L->next;
    // 遍历链表，直到链表末尾
    while (p != NULL)
    {
        // 将节点数据写入文件
        fprintf(fpw, "%s %s %s %s %d %d %d\n", p->id, p->name, p->grade, p->tele, p->room, p->days, p->num);
        // 移动到下一个节点
        p = p->next;
    }
    
    // 关闭文件
    fclose(fpw);
    // 成功保存数据，返回1
    return 1;
}

/**
 * 功能选择函数
 * 根据传入的整型参数x，选择执行不同的功能
 * 此函数不返回任何值
 * 
 * 参数:
 *   x - 一个整数，用于选择执行的功能
 */
void swit(int x)
{
    // 根据x的值选择要执行的操作
    switch(x)
    {
        // 当x为1时，调用Int函数
        case 1: Int(); break;
        
        // 当x为2时，调用Out函数，并传入List的地址
        case 2: Out(&List); break;
        
        // 当x为3时，调用Change函数，并传入List的地址
        case 3: Change(&List); break;
        
        // 当x为4时，调用Inquire函数，并传入List的地址
        case 4: Inquire(&List); break;
        
        // 当x的值不属于上述任何一种情况时，输出错误信息
        default: printf("无效的选择，请重新输入。\n"); break;
    }
}

void Home() // 登录选择界面 
{
    system("cls"); // 清屏，为显示主菜单做准备
    printf("----------------------------\n");
    printf("---     自习室预约管理系统     ---\n");
    printf("----------------------------\n");
    Message(&List); // 显示系统消息或状态，假设List为全局变量或已定义
    printf("---     办理预约信息 ---1       ---\n");
    printf("---     办理结束学习 ---2       ---\n");
    printf("---     修改预约信息 ---3       ---\n");
    printf("---     查询同学信息 ---4       ---\n");
    printf("---     退出程序     ---5       ---\n"); 
    printf("---     请输入对应功能数字："); // 提示用户输入以选择功能
}


int Int() // 办理入住手续
{
    int num = 0, floor = 0, roomNum = 0, confirmNum = 0;
    node st;
    system("cls"); // 清屏，提供一个干净的界面给用户
    statusrooms(); // 显示自习室状态，以便用户选择
    printf("请输入下列信息以方便预约登记\n");

    while (1) // 无限循环，直到用户正确输入所有信息
    {
        printf("自习室号："); 
        scanf("%d", &num);
        floor = (num / 10) - 1; // 根据自习室号计算楼层
        roomNum = (num % 10); // 计算房间号

        if (room[floor][roomNum].state != 0) // 检查自习室是否已被预约
        {
            printf("对不起，该自习室已有同学预约，请重新选择\n");
            continue;
        }

        printf("请再次确认自习室号："); 
        scanf("%d", &confirmNum);

        if (num != confirmNum) // 确认自习室号
        {
            printf("两次输入的自习室号不一致，请重新输入。\n");
            continue;
        }

        room[floor][roomNum].state = 1; // 将自习室状态设置为已预约
        printf("名字："); 
        scanf("%s", st.name);
        strcpy(room[floor][roomNum].userName, st.name); // 保存用户名
        printf("学号：");
        scanf("%s", st.id);
        printf("年级："); 
        scanf("%s", st.grade);
        printf("电话："); 
        scanf("%s", st.tele);

        printf("预约时间："); 
        scanf("%d", &st.days);
        st.num = st.days * 100; // 计算预约编号
        st.room = num; // 设置自习室号
        insertLink(&List, st); // 将预约信息插入链表
        return 1; // 成功预约
    }
}

// 修改预约信息
/**
 * 修改自习室预约信息
 * @param L 链表的头指针
 */
void Change(node *L)
{
    // 初始化变量
    int num0 = 0, num2 = 0, floor = 0, roomNum = 0;
    // 清屏
    system("cls");
    // 初始化选择变量
    int choice = 0;
    // 获取需要修改的节点
    node *st = Inquire(L);
    // 当节点不为空时，进入循环
    while (st != NULL)
    {
        // 显示修改选项
        printf("修改学号 --- 1\n");
        printf("修改姓名     --- 2\n");
        printf("修改年级     --- 3\n");
        printf("修改电话     --- 4\n");
        printf("修改自习室号   --- 5\n");
        printf("修改预约时间 --- 6\n");       
        printf("请输入要修改的信息：");
        scanf("%d", &choice);
        // 根据用户选择进行相应的修改操作
        switch (choice)
        {
            case 1:
                printf("\n请输入新的学号："); 
                scanf("%s", st->id);
                break;
            case 2:
                printf("\n请输入新的姓名："); 
                scanf("%s", st->name);
                break;
            case 3:
                printf("\n请输入新的年级："); 
                scanf("%s", st->grade);
                break;
            case 4:
                printf("\n请输入新的电话："); 
                scanf("%s", st->tele);
                break;
            case 5:
                // 更新自习室号并修改相应自习室的状态
                num0 = st->room;
                floor = (num0 / 10) - 1;
                roomNum = (num0 % 10) ;
                room[floor][roomNum].state = 0;
                printf("\n请输入新的自习室号："); 
                scanf("%d", &st->room);
                num2 = st->room;
                floor = (num2 / 10) - 1;
                roomNum = (num2 % 10) ;
                room[floor][roomNum].state = 1;
                break;
            case 6:
                printf("\n请输入新的预约日期："); 
                scanf("%d", &st->days);
                break;
            default:
                printf("无效的选择，请重新输入。\n");
                continue;
        }
        
        // 更新节点的排序码
        st->num = st->days * 100;
        
        // 显示修改后的信息
        printf("|学号|姓名\t|年级\t|电话\t|自习室号\t|预约时间|\n");
        printf("%s\t  %s\t  %s\t  %s\t  0%d\t  %d\n", st->id, st->name, st->grade, st->tele, st->room, st->days);   
        printf("是否继续修改信息（yes - 1/no - 0）："); 
        scanf("%d", &choice);
        
        // 如果用户选择不再继续，则退出循环
        if (choice == 0)
        {
            break;
        }
    }
    // 保存修改后的信息到文件
    saveFile(L); 
}

/**
 * 插入预约人信息 
 * 该函数将一个新的节点插入到链表中，作为参数传入的节点L的下一个节点。
 * 
 * @param L 链表中的一个节点，新节点将插入为此节点的下一个节点。
 * @param e 要插入的新节点，包含预约人的信息。
 */
void insertLink(node *L, node e)
{
    // 分配新的节点空间
    node *newNode = (node *)malloc(sizeof(node));
    // 检查内存分配是否成功
    if (!newNode) {
        printf("内存分配失败。\n");
        return;
    }
    // 初始化新节点
    *newNode = e;
    // 将新节点链接到链表中
    newNode->next = L->next;
    L->next = newNode;
}

/**
 * 办理退房手续
 * @param L 指向自习室链表的头节点
 */
void Out(node *L)
{
    int num = 0, floor = 0, roomNum = 0;
    // 清屏
    system("cls");
    int room1, choice;
    node *p, *st = NULL;

    // 提示用户输入自习室号
    printf("请输入要查询的自习室号：");
    scanf("%d", &room1);
    num = room1;
    // 计算自习室所在楼层
    floor = (num / 10) - 1;
    // 计算自习室在该楼层的编号
    roomNum = (num % 10) ;
    // 查询指定自习室号的节点
    st = queryRoom(room1, L);

    // 如果未找到指定的自习室节点
    if (st == NULL)
    {
        printf("查无自习室!\n");
        return;
    }
    else
    {
        // 打印自习室使用情况表头
        printf("___________________________________________________________________\n");
        printf("|学号|姓名\t|年级\t|电话\t|自习室号\t|预约时间\n");
        printf("___________________________________________________________________\n");
        // 打印自习室使用情况详细信息
        printf("%s\t  %s\t  %s\t  %s\t  0%d\t  %d\n", st->id, st->name, st->grade, st->tele, st->room, st->days);   
        printf("___________________________________________________________________\n");
    }

    // 向用户告别
    printf("等待同学 %s 下次到来\n", st->name); 

    // 从链表中删除该自习室节点
    deletLink(L, st);
    // 更新自习室状态为可用
    room[floor][roomNum].state = 0;
    // 清空自习室使用人姓名
    strcpy(room[floor][roomNum].userName, "NULL");
    // 保存链表数据到文件
    saveFile(L);
}

/**
 * 删除链表中指定的节点
 * 
 * @param L 链表的头节点指针
 * @param pr 需要删除的节点指针
 * 
 * 说明:
 * 1. 如果头节点或需要删除的节点为空，则直接返回，不进行操作。
 * 2. 通过遍历链表找到需要删除节点的前一个节点。
 * 3. 修改前一个节点的指针，跳过需要删除的节点，从而将其从链表中移除。
 * 4. 释放需要删除节点的内存空间，防止内存泄漏。
 */
void deletLink(node *L, node *pr)
{
    // 检查链表头节点和要删除的节点是否为空
    if (L == NULL || pr == NULL) return;
    
    // 初始化指针s从链表头开始遍历
    node *s = L;
    // 遍历链表，直到找到要删除节点的前一个节点
    while (s->next != pr) {
        s = s->next;
    }
    
    // 将前一个节点的next指针指向要删除节点的下一个节点，移除要删除的节点
    s->next = pr->next;
    
    // 释放要删除节点的内存空间
    free(pr);
}

node * Inquire(node *L) // 查询预约信息
{
    // 清屏，以便更好地显示查询结果
    system("cls");
    int choice;
    char id[20];
    char name[50]; 
    int room;
    node *st;

    // 提示用户选择查询方式
    printf("请选择一种方式\n");
    printf("学号--1\n");
    printf("姓名------2\n"); 
    printf("自习室号----3\n"); 
    printf("请输入方式：");
    scanf("%d", &choice);

    // 根据用户选择的查询方式执行相应的查询操作
    if (choice == 1)
    {
        // 提示用户输入要查询的学号
        printf("请输入要查询的学号：");
        scanf("%s", id);
        // 调用queryNumber函数根据学号查询学生信息
        st = queryNumber(id, L); 

        // 如果查询结果为空，则提示用户并返回NULL
        if (st == NULL)
        {
            printf("查无此人!\n");
            return NULL;
        }
        else
        {
            // 显示查询结果
            printf("___________________________________________________________________\n");
            printf("|学号|姓名\t|年级\t|电话\t|自习室号\t|预约时间|\n");
            printf("___________________________________________________________________\n");
            printf("%s\t  %s\t  %s\t  %s\t  0%d\t  %d\n", st->id, st->name, st->grade, st->tele, st->room, st->days);   
            printf("___________________________________________________________________\n");
        }
    }
    else if (choice == 2)
    {   
        // 提示用户输入要查询的姓名
        printf("请输入要查询的姓名：");
        scanf("%s", name);
        // 调用queryName函数根据姓名查询学生信息
        st = queryName(name, L); 

        // 如果查询结果为空，则提示用户并返回NULL
        if (st == NULL)
        {
            printf("查无此人!\n");
            return NULL;
        }
        else
        {
            // 显示查询结果
            printf("___________________________________________________________________\n");
            printf("|学号|姓名\t|年级\t|电话\t|自习室号\t|预约时间|\n");
            printf("___________________________________________________________________\n");
            printf("%s\t  %s\t  %s\t  %s\t  0%d\t  %d\n", st->id, st->name, st->grade, st->tele, st->room, st->days);   
            printf("___________________________________________________________________\n");
        }
    }
    else if (choice == 3)
    {
        // 提示用户输入要查询的自习室号
        printf("请输入要操作的自习室号：");
        scanf("%d", &room);
        // 调用queryRoom函数根据自习室号查询学生信息
        st = queryRoom(room, L); 

        // 如果查询结果为空，则提示用户并返回NULL
        if (st == NULL)
        {
            printf("查无此人!\n");
            return NULL;
        }
        else
        {
            // 显示查询结果
            printf("___________________________________________________________________\n");
            printf("|学号|姓名\t|年级\t|电话\t|自习室号\t|预约时间|\n");
            printf("___________________________________________________________________\n");
            printf("%s\t  %s\t  %s\t  %s\t  0%d\t  %d\n", st->id, st->name, st->grade, st->tele, st->room, st->days);   
            printf("___________________________________________________________________\n");
        }
    }

    // 返回查询结果
    return st;
}

/**
 * 按学号进行查找
 * 
 * 本函数通过遍历链表来查找指定学号的节点如果找到匹配的学号，则返回该节点；
 * 如果遍历完整个链表都没有找到匹配的学号，则返回NULL
 * 
 * @param id 需要查找的学号，以字符数组形式传入
 * @param L 链表的头指针，用于开始遍历
 * @return 如果找到匹配的学号，返回指向该节点的指针；否则返回NULL
 */
node * queryNumber(char id[], node *L) // 按学号进行查找 
{
    // 从链表的第一个实际节点开始遍历
    node *p = L->next;
    while (p != NULL)
    {
        // 使用strcmp比较id和当前节点的id如果相等，返回当前节点
        if (strcmp(id, p->id) == 0)
        {
            return p;
        }
        // 移动到下一个节点
        p = p->next;
    }
    // 如果遍历完整个链表都没有找到匹配的节点，返回NULL
    return NULL;
}

/**
 * 按名字进行查找
 * 
 * @param name 要查找的名字
 * @param L 链表的头指针
 * @return 如果找到，则返回指向具有指定名字的节点的指针；如果未找到，则返回NULL
 */
node * queryName(char name[], node *L)
{
    // 从链表的第一个实际节点开始遍历
    node *p = L->next;
    // 遍历链表中的所有节点
    while (p != NULL)
    {
        // 比较当前节点的名字与要查找的名字
        if (strcmp(name, p->name) == 0)
        {
            // 如果找到匹配的名字，返回当前节点的指针
            return p;
        }
        // 移动到链表的下一个节点
        p = p->next;
    }
    // 如果遍历完整个链表都没有找到匹配的名字，返回NULL
    return NULL;
}

// 按自习室号进行查找
// 参数: room - 需要查找的自习室号, L - 链表头指针
// 返回值: 返回指向找到的节点的指针，如果未找到则返回NULL
node * queryRoom(int room, node *L)
{
    // 从链表的第一个实际节点开始遍历
    node *p = L->next;
    // 遍历链表直到找到自习室号匹配的节点
    while (p != NULL)
    {
        // 如果当前节点的自习室号与要查找的自习室号匹配，则返回该节点
        if (p->room == room)
        {
            return p;
        }
        // 移动到链表的下一个节点
        p = p->next;
    } 
    // 如果链表中没有找到匹配的自习室号，则返回NULL
    return NULL;
}

// 输出预约信息
void Message(node *L)
{
    // 初始化房间数组
    roomarray_Init(L);
    // 更新房间状态
    statusrooms();
    // 遍历预约列表
    node *p = L->next;
    // 打印表头
    printf("___________________________________________________________________\n");
    printf("|学号|姓名\t|年级\t|电话\t|自习室号\t|预约时间|\n");
    printf("___________________________________________________________________\n");
    // 遍历并打印每个预约信息
    while (p != NULL)
    {
        // 打印预约详情
        printf("%s\t  %s\t  %s\t  %s\t  0%d\t       %d\n", p->id, p->name, p->grade, p->tele, p->room, p->days);
        printf("___________________________________________________________________\n");
        p = p->next;
    }
}

int Sign() // 登录程序
{
    char c;
    char passw[] = "111"; // 正确的密码
    char test[20] = {0}; // 用户输入的密码
    int count = 0; // 用来记录登录次数

    while (count < 3)
    {
        printf("-------登录系统---------\n");
        printf("请输入系统密码:");
        int i = 0; // 记录密码长度
        while (1) 
        {
            c = _getch(); // 用 _getch() 函数输入，字符不会显示在屏幕上
            if (c == '\r') 
            { // 遇到回车，表明密码输入结束
                break; // while 循环的出口
            }
            else if (c == '\b') 
            { // 遇到退格，需要删除前一个星号
                printf("\b \b");  // 退格，打一个空格，再退格，实质上是用空格覆盖掉星号
                --i;
            }
            else {
                test[i++] = c; // 将字符放入数组
                printf("*"); // 显示星号代表密码
            }
        }
        if (strcmp(passw, test) == 0)
        {
            printf("\n登陆成功\n");
            break;
        }
        else
        {
            printf("密码错误\n");
            count++;
        }
    }
    if (count == 3)
    {
        printf("退出程序\n");
        Exit();
    }
    return 0;
}

void Exit() // 退出程序
{
    system("cls");
    printf("欢迎下次使用~\n");
    exit(0); 
}

void statusrooms() //显示自习室状态
{   
    // 清屏，为显示自习室状态提供清晰的界面
    system("cls");
    
    // 打印自习室状态标题
    printf("自习室状态：\n");
    
    // 遍历自习室数组，显示每个自习室的状态
    for (int i = 0; i < 5; i++)
    {
        for (int j = 0; j < 5; j++)
        {
            // 打印自习室编号
            printf("%d%d\t", i + 1, j );
            
            // 判断当前自习室是否已入住
            if (room[i][j].state == 0) 
            {
                // 如果自习室未被占用，则显示为"空闲"
                printf("空闲\t");
            }
            else
            {
                // 如果自习室已被占用，则显示为"已入住"
                printf("已入住\t");
            }
        }
        
        // 换行，准备显示下一排自习室的状态
        printf("\n");
    }
}

/* 初始化时，遍历链表，使文件读取的值赋给结构体数组，完成初始化*/ 
/**
 * 初始化房间数组
 * 该函数根据链表中的数据，初始化一个5x5的房间数组
 * 链表中的每个节点代表一个房间，包含房间号和居住者姓名
 * 房间号的格式为两位数，其中十位表示楼层，个位表示房间号
 * 函数将链表中的房间信息复制到一个二维数组中，便于后续处理
 * 
 * @param L 链表的头指针
 */
void roomarray_Init(node *L) // 结构体数组初始化 roomarray_Init(&List) 
{   
    // 从链表的头节点开始遍历
    node *p = L->next;
    // 如果链表不为空，则进行初始化
    if (p != NULL)
    {
        // 遍历5x5的房间数组
        for (int i = 0; i < 5; i++)
        {
            for (int j = 0; j < 5; j++)
            {
                // 遍历链表中的每个房间
                while (p != NULL)
                {
                    // 获取当前房间的房间号
                    int num = p->room;
                    // 计算房间所在的楼层
                    int floor = (num / 10) - 1;
                    // 计算房间在当前楼层的位置
                    int roomNum = (num % 10) ;
                    // 如果房间位置与当前遍历的数组位置匹配
                    if (i == floor && j == roomNum)
                    {
                        // 将链表中该房间的居住者姓名复制到数组中
                        strcpy(room[i][j].userName, p->name);
                        // 设置房间状态为已入住
                        room[i][j].state = 1;
                        // 跳出循环，继续遍历下一个房间
                        break;
                    }
                    // 继续遍历链表中的下一个房间
                    p = p->next;
                }
            }
        }
    }
}
