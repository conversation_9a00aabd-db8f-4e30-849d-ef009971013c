#include <stdio.h>
#include <stdlib.h>
#include <time.h>
#include <unistd.h>
#include <math.h>

#define WIDTH 80
#define HEIGHT 25
#define NUM_PARTICLES 50

typedef struct {
    int x;
    int y;
    double vx;
    double vy;
    int life;
    int color;
} Particle;

typedef struct {
    Particle particles[NUM_PARTICLES];
} Firework;

// ANSI颜色代码
const char* colors[] = {
    "\033[31m",  // 红色
    "\033[32m",  // 绿色
    "\033[33m",  // 黄色
    "\033[34m",  // 蓝色
    "\033[35m",  // 紫色
    "\033[36m",  // 青色
    "\033[0m"    // 重置颜色
};

void init_firework(Firework *fw, int x, int y) {
    for (int i = 0; i < NUM_PARTICLES; i++) {
        fw->particles[i].x = x;
        fw->particles[i].y = y;
        double angle = (rand() % 360) * M_PI / 180.0;
        double speed = (rand() % 10) + 2;
        fw->particles[i].vx = speed * cos(angle);
        fw->particles[i].vy = speed * sin(angle);
        fw->particles[i].life = (rand() % 20) + 10;
        fw->particles[i].color = rand() % 6;  // 随机选择颜色
    }
}

void update_firework(Firework *fw) {
    for (int i = 0; i < NUM_PARTICLES; i++) {
        Particle *p = &fw->particles[i];
        p->x += p->vx;
        p->y += p->vy;
        p->vy += 0.1;  // 模拟重力
        p->life--;
    }
}

void draw_firework(Firework fw) {
    printf("\033[2J\033[H");  // 清屏并定位光标

    for (int y = 0; y < HEIGHT; y++) {
        for (int x = 0; x < WIDTH; x++) {
            int printed = 0;
            for (int i = 0; i < NUM_PARTICLES; i++) {
                if (fabs(fw.particles[i].x - x) < 0.5 && fabs(fw.particles[i].y - y) < 0.5 && fw.particles[i].life > 0) {
                    printf("%s*\033[0m", colors[fw.particles[i].color]);
                    printed = 1;
                    break;
                }
            }
            if (!printed) {
                printf(" ");
            }
        }
        printf("\n");
    }
}

int main() {
    srand(time(NULL));

    Firework fw;
    int x = WIDTH / 2;
    int y = HEIGHT;

    while (1) {
        init_firework(&fw, x, y);
        for (int i = 0; i < 30; i++) {  // 模拟烟花爆炸过程
            update_firework(&fw);
            draw_firework(fw);
            usleep(50000);  // 控制更新速度
        }
        usleep(1000000);  // 等待下一发烟花
    }

    return 0;
}