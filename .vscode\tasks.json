{"version": "2.0.0", "tasks": [{"type": "cppbuild", "label": "C/C++: g++.exe 生成活动文件", "command": "g++", "args": ["-fdiagnostics-color=always", "-g", "-std=c++11", "-Wall", "-Wextra", "${file}", "-o", "${fileDirname}\\${fileBasenameNoExtension}.exe"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$gcc"], "group": {"kind": "build", "isDefault": true}, "detail": "编译器: g++.exe"}, {"type": "cppbuild", "label": "C/C++: cl.exe 生成活动文件", "command": "cl.exe", "args": ["/Zi", "/EHsc", "/nologo", "/Fe${fileDirname}\\${fileBasenameNoExtension}.exe", "${file}"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$msCompile"], "group": "build", "detail": "编译器: cl.exe"}]}