#include <ctime>
#include <cstdio>
#include <cmath>
#include <cstdlib>
#include <iostream>
#include <cstring>

#ifdef _WIN32
    #include <windows.h>
    #include <conio.h>
    // 禁用弃用函数警告
    #pragma warning(disable: 4996)
#else
    #include <termios.h>
    #include <unistd.h>
    #include <sys/select.h>
    #include <fcntl.h>
#endif

// 跨平台函数声明
#ifdef _WIN32
    void gotoxy(int x, int y);
    void color(int b);
    void hideCursor();
    int kbhit();
    int getch();
    void Sleep(int ms);
#else
    void gotoxy(int x, int y);
    void color(int b);
    void hideCursor();
    int kbhit();
    int getch();
    void Sleep(int ms);
    void system(const char* cmd);
#endif

// 函数声明
void set_difficulty(int level);
void drawBlock(int x, int y, int color);
void clearBlock(int x, int y);

// 游戏速度和难度设置
#define Speed 600
#define EASY_SPEED 800
#define NORMAL_SPEED 600
#define HARD_SPEED 400

// 游戏状态枚举
enum GameState {
    MENU,
    PLAYING,
    PAUSED,
    GAME_OVER
};

// 全局变量
int board[30][30][2];
int sort[7] = { 0, 1, 3, 5, 7, 11, 15 };
int mark = 0;
int amend_x = 10, amend_y = 4, speed = Speed;
time_t seed;
int kind = 0, next_kind;
int key;
int win = 1;
int pause = 0;
int difficulty = 1;
char difficulty_names[3][10] = { "Easy", "Normal", "Hard" };
enum GameState currentState = MENU;
int selectedDifficulty = 1;

// 方块动态结构体
struct now {
    int x[4];
    int y[4];
    int color;
} now[19] = {
    {{0, 2, 0, 2}, {-1, -1, -2, -2}, 10}, //方块系 num:0
    {{0, 6, 4, 2}, {-1, -1, -1, -1}, 11}, {{2, 2, 2, 2}, {-1, -2, -3, -4}, 11}, //长条系 num:1-2 
    {{0, 4, 2, 2}, {-2, -1, -1, -2}, 12}, {{0, 2, 0, 2}, {-1, -2, -2, -3}, 12}, //  L系 num:3-4;
    {{0, 4, 2, 2}, {-1, -2, -1, -2}, 13}, {{0, 2, 2, 0}, {-2, -1, -2, -3}, 13}, //反L 系 num:5-6;
    {{0, 4, 2, 2}, {-1, -1, -1, -2}, 14}, {{0, 2, 0, 0}, {-1, -2, -2, -3}, 14}, {{0, 4, 2, 2}, {-2, -2, -1, -2}, 14}, {{0, 2, 2, 2}, {-2, -1, -2, -3}, 14}, //T方块系 num:7-10
    {{0, 4, 2, 0}, {-1, -1, -1, -2}, 9}, {{0, 2, 0, 0}, {-1, -3, -2, -3}, 9}, {{0, 4, 2, 4}, {-2, -1, -2, -2}, 9}, {{0, 2, 2, 2}, {-1, -1, -2, -3}, 9}, //Z方块系 num:11-14
    {{0, 4, 2, 4}, {-1, -1, -1, -2}, 5}, {{0, 2, 0, 0}, {-1, -1, -2, -3}, 5}, {{0, 4, 2, 0}, {-1, -2, -2, -2}, 5}, {{0, 2, 2, 2}, {-3, -1, -2, -3}, 5}  //S方块系 num:15-18
};

// 跨平台函数实现
#ifdef _WIN32
// Windows 实现
void gotoxy(int x, int y) {
    COORD pos = { static_cast<SHORT>(x), static_cast<SHORT>(y) };
    SetConsoleCursorPosition(GetStdHandle(STD_OUTPUT_HANDLE), pos);
}

void color(int b) {
    SetConsoleTextAttribute(GetStdHandle(STD_OUTPUT_HANDLE), b);
}

void hideCursor() {
    CONSOLE_CURSOR_INFO cursorInfo;
    GetConsoleCursorInfo(GetStdHandle(STD_OUTPUT_HANDLE), &cursorInfo);
    cursorInfo.bVisible = FALSE;
    SetConsoleCursorInfo(GetStdHandle(STD_OUTPUT_HANDLE), &cursorInfo);
}

#else
// Linux/Unix 实现
void gotoxy(int x, int y) {
    printf("\033[%d;%dH", y + 1, x + 1);
}

void color(int b) {
    // ANSI 颜色代码映射
    int ansi_colors[] = {0, 4, 2, 6, 1, 5, 3, 7, 8, 12, 10, 14, 9, 13, 11, 15};
    if (b >= 0 && b < 16) {
        if (b < 8) {
            printf("\033[3%dm", ansi_colors[b]);
        } else {
            printf("\033[9%dm", ansi_colors[b] - 8);
        }
    }
}

void hideCursor() {
    printf("\033[?25l");
}

int kbhit() {
    struct termios oldt, newt;
    int ch;
    int oldf;
    
    tcgetattr(STDIN_FILENO, &oldt);
    newt = oldt;
    newt.c_lflag &= ~(ICANON | ECHO);
    tcsetattr(STDIN_FILENO, TCSANOW, &newt);
    oldf = fcntl(STDIN_FILENO, F_GETFL, 0);
    fcntl(STDIN_FILENO, F_SETFL, oldf | O_NONBLOCK);
    
    ch = getchar();
    
    tcsetattr(STDIN_FILENO, TCSANOW, &oldt);
    fcntl(STDIN_FILENO, F_SETFL, oldf);
    
    if(ch != EOF) {
        ungetc(ch, stdin);
        return 1;
    }
    
    return 0;
}

int getch() {
    struct termios oldt, newt;
    int ch;
    tcgetattr(STDIN_FILENO, &oldt);
    newt = oldt;
    newt.c_lflag &= ~(ICANON | ECHO);
    tcsetattr(STDIN_FILENO, TCSANOW, &newt);
    ch = getchar();
    tcsetattr(STDIN_FILENO, TCSANOW, &oldt);
    return ch;
}

void Sleep(int ms) {
    usleep(ms * 1000);
}

void system(const char* cmd) {
    if (strcmp(cmd, "cls") == 0) {
        printf("\033[2J\033[H");
    } else {
        ::system(cmd);
    }
}
#endif

// 绘制方块（只用一个"█"字符，x坐标*2）
void drawBlock(int x, int y, int blockColor) {
    gotoxy(x * 2, y);
    color(blockColor);
    printf("█");
    color(7);
}

// 清除方块
void clearBlock(int x, int y) {
    gotoxy(x * 2, y);
    printf(" ");
}

// 显示主菜单
void showMainMenu() {
    system("cls");
    color(15); // 白色
    gotoxy(30, 5);
    printf("=== 俄罗斯方块 ===");

    color(14); // 黄色
    gotoxy(28, 8);
    printf("请选择难度:");

    for (int i = 0; i < 3; i++) {
        if (i + 1 == selectedDifficulty) {
            color(12); // 红色
            gotoxy(28, 10 + i);
            printf("-> %s", difficulty_names[i]);
        }
        else {
            color(15); // 白色
            gotoxy(30, 10 + i);
            printf("%s", difficulty_names[i]);
        }
    }

    color(10); // 绿色
    gotoxy(28, 14);
    printf("按 Enter 开始游戏");
    gotoxy(28, 15);
    printf("按 ESC 退出游戏");
}

// 处理菜单输入
void handleMenuInput() {
    int key = getch();
    if (key == 224) { // 方向键
        key = getch();
        if (key == 80 && selectedDifficulty < 3) { // 下箭头
            selectedDifficulty++;
        }
        else if (key == 72 && selectedDifficulty > 1) { // 上箭头
            selectedDifficulty--;
        }
        showMainMenu();
    }
    else if (key == 13) { // Enter
        difficulty = selectedDifficulty;
        currentState = PLAYING;
        system("cls");
    }
    else if (key == 27) { // ESC
        exit(0);
    }
}

// 显示游戏边界
void boundary() {
    int i = 0, j = 0;
    gotoxy(2, 1);
    for (i = 0; i < 26; i++) {
        printf("█");
    }
    gotoxy(2, 27);
    for (i = 0; i < 26; i++) {
        printf("█");
    }
    for (i = 0; i < 25; i++) {
        gotoxy(2, 2 + i);
        printf("█");
        gotoxy(34, 2 + i);
        if (i == 7 || i == 13) {
            printf("████");
        }
        else {
            printf("█");
        }
        gotoxy(52, 2 + i);
        printf("█");
    }

    // 显示难度信息
    color(7);
    gotoxy(37, 9);
    printf("Difficulty: %s", difficulty_names[difficulty - 1]);

    int t = 100;
    color(7);
    gotoxy(37, 11);
    printf("Highest: %d", t);
    color(7);
    gotoxy(37, 13);
    printf("Now: %d", mark);
    gotoxy(37, 17);
    printf("↑:Rotate");
    gotoxy(37, 19);
    printf("↓:Expedite");
    gotoxy(37, 21);
    printf("←:Left ");
    gotoxy(37, 23);
    printf("→:Right");
    gotoxy(37, 25);
    printf("Space:pause");
    color(12);
    gotoxy(3, 29);
    gotoxy(11, 30);
    printf("Tetris Game");
    color(7);
    for (int i = 0; i < 30; ++i) {
        board[26][i][0] = 1;
    }
}

// 设置难度等级
void set_difficulty(int level) {
    if (level < 1) level = 1;
    if (level > 3) level = 3;
    difficulty = level;

    // 更新难度显示
    gotoxy(37, 9);
    color(7);
    printf("Difficulty: %s", difficulty_names[difficulty - 1]);

    // 根据难度调整速度
    switch (difficulty) {
    case 1: speed = EASY_SPEED; break;
    case 2: speed = NORMAL_SPEED; break;
    case 3: speed = HARD_SPEED; break;
    }
}

// 简单的游戏循环
void playGame() {
    boundary();
    set_difficulty(difficulty);

    gotoxy(10, 15);
    color(14);
    printf("游戏开始！");
    gotoxy(10, 17);
    printf("按任意键继续...");
    getch();

    gotoxy(10, 15);
    printf("                ");
    gotoxy(10, 17);
    printf("                ");

    // 简单的游戏循环
    int gameTime = 0;
    while (currentState == PLAYING) {
        if (kbhit()) {
            int key = getch();
            if (key == 27) { // ESC
                currentState = MENU;
                break;
            }
            if (key == 32) { // Space
                gotoxy(10, 15);
                color(12);
                printf("游戏暂停 - 按空格继续");
                while (1) {
                    if (kbhit() && getch() == 32) {
                        gotoxy(10, 15);
                        printf("                    ");
                        break;
                    }
                    Sleep(100);
                }
            }
        }

        // 简单的游戏逻辑
        gameTime++;
        if (gameTime % 100 == 0) {
            mark += 10;
            gotoxy(37, 13);
            printf("Now: %d", mark);
        }

        Sleep(50);

        // 游戏结束条件
        if (gameTime > 2000) {
            currentState = GAME_OVER;
        }
    }
}

int main() {
    srand(static_cast<unsigned int>(time(&seed)));
    
    // 隐藏光标
    hideCursor();
    
    // 主循环
    while (1) {
        switch (currentState) {
        case MENU:
            showMainMenu();
            while (currentState == MENU) {
                if (kbhit()) {
                    handleMenuInput();
                }
            }
            break;
            
        case PLAYING:
            printf("Game is starting...\n");
            printf("Press any key to exit...\n");
            getch();
            currentState = MENU;
            break;
            
        case GAME_OVER:
            printf("Game Over!\n");
            getch();
            currentState = MENU;
            break;
        }
    }
    
    return 0;
}
