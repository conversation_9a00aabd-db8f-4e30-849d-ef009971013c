# Makefile for 俄罗斯方块 (Tetris)

# 编译器设置
CXX = g++
CXXFLAGS = -std=c++11 -Wall -Wextra

# 目标文件名
TARGET = tetris
SOURCE = 俄罗斯方块.cpp

# Windows 特定设置
ifeq ($(OS),Windows_NT)
    TARGET = tetris.exe
    CXXFLAGS += -static-libgcc -static-libstdc++
endif

# 默认目标
all: $(TARGET)

# 编译规则
$(TARGET): $(SOURCE)
	$(CXX) $(CXXFLAGS) -o $(TARGET) $(SOURCE)

# 清理规则
clean:
ifeq ($(OS),Windows_NT)
	del /f $(TARGET) 2>nul || true
else
	rm -f $(TARGET)
endif

# 运行程序
run: $(TARGET)
	./$(TARGET)

# 帮助信息
help:
	@echo "可用的命令:"
	@echo "  make        - 编译程序"
	@echo "  make run    - 编译并运行程序"
	@echo "  make clean  - 清理编译文件"
	@echo "  make help   - 显示此帮助信息"

.PHONY: all clean run help
