@echo off
setlocal enabledelayedexpansion
chcp 65001 >nul 2>&1

echo ========================================
echo           智能编译脚本
echo ========================================

set "SOURCE_FILE=%~1"
if "%SOURCE_FILE%"=="" set "SOURCE_FILE=俄罗斯方块.cpp"

echo 源文件: %SOURCE_FILE%
echo.

REM 获取文件名（不含扩展名）
for %%F in ("%SOURCE_FILE%") do (
    set "OUTPUT_FILE=%%~nF.exe"
    set "FILE_DIR=%%~dpF"
)

echo 输出文件: %OUTPUT_FILE%
echo 工作目录: %FILE_DIR%
echo.

REM 切换到文件目录
cd /d "%FILE_DIR%"

REM 检查编译器并编译
echo 正在检测可用的编译器...

REM 尝试 g++
echo 检查 g++...
where g++ >nul 2>&1
if !errorlevel! equ 0 (
    echo ✓ 找到 g++ 编译器
    echo 正在使用 g++ 编译...
    g++ -std=c++11 -Wall -Wextra -static-libgcc -static-libstdc++ -o "%OUTPUT_FILE%" "%SOURCE_FILE%"
    if !errorlevel! equ 0 (
        echo ✓ 编译成功！
        echo 可执行文件: %OUTPUT_FILE%
        goto :success
    ) else (
        echo ✗ g++ 编译失败
    )
)

REM 尝试 gcc
echo 检查 gcc...
where gcc >nul 2>&1
if !errorlevel! equ 0 (
    echo ✓ 找到 gcc 编译器
    echo 正在使用 gcc 编译...
    gcc -std=c++11 -lstdc++ -static-libgcc -static-libstdc++ -o "%OUTPUT_FILE%" "%SOURCE_FILE%"
    if !errorlevel! equ 0 (
        echo ✓ 编译成功！
        echo 可执行文件: %OUTPUT_FILE%
        goto :success
    ) else (
        echo ✗ gcc 编译失败
    )
)

REM 尝试 cl (Visual Studio)
echo 检查 cl...
where cl >nul 2>&1
if !errorlevel! equ 0 (
    echo ✓ 找到 cl 编译器
    echo 正在使用 cl 编译...
    cl /EHsc /Fe:"%OUTPUT_FILE%" "%SOURCE_FILE%" >nul 2>&1
    if !errorlevel! equ 0 (
        echo ✓ 编译成功！
        echo 可执行文件: %OUTPUT_FILE%
        REM 清理 cl 生成的临时文件
        del *.obj >nul 2>&1
        goto :success
    ) else (
        echo ✗ cl 编译失败
    )
)

REM 如果所有编译器都失败
echo.
echo ❌ 错误：未找到可用的 C++ 编译器！
echo.
echo 请安装以下任一编译器：
echo 1. MinGW-w64: https://www.mingw-w64.org/downloads/
echo 2. Visual Studio: https://visualstudio.microsoft.com/zh-hans/vs/community/
echo 3. Dev-C++: https://sourceforge.net/projects/orwelldevcpp/
echo.
echo 安装后请重启 VS Code 并确保编译器在系统 PATH 中。
echo.
goto :end

:success
echo.
echo ========================================
echo           编译完成！
echo ========================================
echo.

:end
endlocal
