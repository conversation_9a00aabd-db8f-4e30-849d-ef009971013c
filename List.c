bool bracketCheck(char str[],int length){
    SqStack S;
    InitStack(&S);
    for (int i = 0; i < length; i++)
    {
        if(str[i]=='('||str[i]=='{'||str[i]=='['){
            Push(&S,str[i]);
        }
        else {
            if(StackEmpty(S)){
                return false;
            }
            char top;
            Pop(&S,&top);
            if(str[i]==')'&&top!='('||str[i]=='}'&&top!='{'||str[i]==']'&&top!='['){
                return false;
            }
        }
    }
    return StackEmpty(S);
    
}