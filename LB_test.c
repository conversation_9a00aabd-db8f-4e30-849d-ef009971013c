struct node {    
    int num;    
    node* next;    
};
void printListReverse(node* head) {    
    if (head == nullptr) {    
        return; // 递归终止条件    
    }    
    printListReverse(head->next);  // 先递归输出后续节点    
    cout << head->num << endl;     // 再输出当前节点    
}
node* createList(int n) {    
    if (n <= 0) {    
        return nullptr; // 如果n小于等于0，返回空链表    
    }    
    node* head = new node;    
    head->num = 1;    
    head->next = nullptr;    
    node* current = head;    
    for (int i = 2; i <= n; ++i) {    
        node* newNode = new node;    
        newNode->num = i;    
        newNode->next = nullptr;    
        current->next = newNode;    
        current = newNode;    
    }    
    return head;    
}
int main() {    
    int n;    
    cout << " ";    
    cin >> n;    
    node* list = createList(n);    
    cout << " " << endl;    
    printListReverse(list);    
    // 释放链表内存（这里省略了，但在实际使用中应该释放）    
    return 0;    
}