# 俄罗斯方块游戏

这是一个跨平台的俄罗斯方块游戏，使用 C++ 编写。

## 关于 windows.h 问题的解决方案

如果您遇到 "没有 windows.h" 的错误，这里提供了几种解决方案：

### 方案 1: 安装 C++ 编译器

#### Windows 系统:
1. **MinGW-w64** (推荐)
   - 下载: https://www.mingw-w64.org/downloads/
   - 或使用 MSYS2: https://www.msys2.org/

2. **Dev-C++** (包含编译器)
   - 下载: https://sourceforge.net/projects/orwelldevcpp/

3. **Code::Blocks** (包含编译器)
   - 下载: http://www.codeblocks.org/downloads

4. **Visual Studio Community** (免费)
   - 下载: https://visualstudio.microsoft.com/zh-hans/vs/community/

#### Linux 系统:
```bash
# Ubuntu/Debian
sudo apt-get install g++

# CentOS/RHEL/Fedora
sudo yum install gcc-c++
# 或者 (较新版本)
sudo dnf install gcc-c++
```

#### macOS 系统:
```bash
# 安装 Xcode 命令行工具
xcode-select --install

# 或使用 Homebrew
brew install gcc
```

### 方案 2: 使用提供的编译脚本

#### Windows:
双击运行 `compile.bat` 文件

#### Linux/macOS:
```bash
chmod +x compile.sh
./compile.sh
```

### 方案 3: 手动编译

如果您已经安装了 g++ 编译器，可以使用以下命令编译：

```bash
# Windows
g++ -std=c++11 -Wall -Wextra -static-libgcc -static-libstdc++ -o tetris.exe "俄罗斯方块.cpp"

# Linux/macOS
g++ -std=c++11 -Wall -Wextra -o tetris "俄罗斯方块.cpp"
```

## 游戏特性

- 跨平台支持 (Windows, Linux, macOS)
- 三种难度等级 (Easy, Normal, Hard)
- 经典的俄罗斯方块玩法
- 彩色方块显示
- 暂停功能
- 分数统计

## 游戏控制

- **↑**: 旋转方块
- **↓**: 加速下落
- **←**: 向左移动
- **→**: 向右移动
- **Space**: 暂停/恢复游戏
- **P**: 切换难度 (游戏中)
- **ESC**: 退出游戏 (主菜单)
- **Enter**: 开始游戏 (主菜单)

## 代码修改说明

原始代码使用了 Windows 特定的 `windows.h` 头文件。为了实现跨平台兼容性，我们做了以下修改：

1. 添加了条件编译指令 (`#ifdef _WIN32`)
2. 为 Linux/Unix 系统实现了等效的函数
3. 使用 ANSI 转义序列实现颜色和光标控制
4. 实现了跨平台的键盘输入处理

## 故障排除

### 编译错误
- 确保安装了 C++ 编译器
- 检查编译器是否在系统 PATH 中
- 尝试使用不同的编译器 (g++, clang++, cl)

### 运行时问题
- 确保终端支持 ANSI 转义序列 (Linux/macOS 默认支持)
- Windows 用户可能需要使用 Windows Terminal 或启用 ANSI 支持

### 字符显示问题
- 确保终端支持 UTF-8 编码
- 某些终端可能无法正确显示方块字符 (█)

## 许可证

此项目仅供学习和娱乐使用。
