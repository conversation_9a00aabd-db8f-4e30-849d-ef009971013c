#include <stdlib.h>  
#include <stdio.h>  
#include <iostream>  
using namespace std;  
  
#define INIT_SIZE 5  
#define INCREMENT 10  
  
/* 定义ElemType为int类型 */  
typedef int ElemType;  
  
/* 顺序表类型定义 */  
typedef struct {  
    ElemType *elem;    // 存储空间基地址  
    int length;        // 当前长度  
    int listsize;      // 当前分配的存储容量  
} SqList;  
  
// 顺序表的基本操作函数声明  
void InitList(SqList &L);  
int ListInsert(SqList &L, int i, ElemType e);  
int ListDelete(SqList &L, int i, ElemType &e);  
void ListTraverse(SqList L, void (*vi)(ElemType));  
  
// ElemType类型元素的基本操作函数声明  
void input(ElemType &s);  
void output(ElemType s);  
int equals(ElemType a, ElemType b);  
  
// ElemType类型元素的基本操作函数实现  
void input(ElemType &s) {  
    cin >> s;  
}  
  
void output(ElemType s) {  
    cout << s << " ";  
}  
  
int equals(ElemType a, ElemType b) {  
    return a == b;  
}  
  
// 顺序表的基本操作函数实现  
void InitList(SqList &L) {  
    L.elem = (ElemType *)malloc(INIT_SIZE * sizeof(ElemType));  
    if (!L.elem) {  
        cerr << "Memory allocation failed!" << endl;  
        exit(1); // 存储分配失败，退出程序  
    }  
    L.length = 0;  
    L.listsize = INIT_SIZE;  
}  
  
int ListInsert(SqList &L, int i, ElemType e) {  
    if (i < 1 || i > L.length + 1) {  
        return 0; // i值不合法  
    }  
    if (L.length >= L.listsize) {  
        ElemType *newbase = (ElemType *)realloc(L.elem, (L.listsize + INCREMENT) * sizeof(ElemType));  
        if (!newbase) {  
            cerr << "Memory reallocation failed!" << endl;  
            exit(1); // 存储分配失败，退出程序  
        }  
        L.elem = newbase;  
        L.listsize += INCREMENT;  
    }  
    ElemType *q = L.elem + i - 1;  
    for (ElemType *p = L.elem + L.length - 1; p >= q; --p) {  
        *(p + 1) = *p;  
    }  
    *q = e;  
    ++L.length;  
    return 1;  
}  
  
int ListDelete(SqList &L, int i, ElemType &e) {  
    if (i < 1 || i > L.length) {  
        return 0; // i值不合法  
    }  
    e = L.elem[i - 1]; // 注意这里使用i-1，因为数组是从0开始的，而顺序表是从1开始的  
    ElemType *p = L.elem + i - 1;  
    for (ElemType *q = p; q < L.elem + L.length - 1; ++q) {  
        *q = *(q + 1);  
    }  
    --L.length;  
    return 1;  
}  
  
void ListTraverse(SqList L, void (*vi)(ElemType)) {  
    ElemType *p = L.elem;  
    for (int i = 0; i < L.length; ++i) { // 注意这里使用i<L.length，因为数组是从0开始的  
        vi(*(p++));  
    }  
    cout << endl;  
}  
  
// 主函数  
int main() {  
    SqList list;  
    ElemType e;  
  
    // 初始化顺序表  
    InitList(list);  
  
    // 插入元素  
    ListInsert(list, 1, 10);  
    ListInsert(list, 2, 20);  
    ListInsert(list, 3, 30);  
  
    // 遍历并输出顺序表  
    cout << "List after insertion: ";  
    ListTraverse(list, output);  
  
    // 删除元素  
    if (ListDelete(list, 2, e)) {  
        cout << "Deleted element: " << e << endl;  
    }  
  
    // 遍历并输出顺序表  
    cout << "List after deletion: ";  
    ListTraverse(list, output);  
  
    // 释放顺序表占用的内存  
    free(list.elem);  
  
    return 0;  
}