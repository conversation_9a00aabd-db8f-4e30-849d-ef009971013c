#include<stdio.h>
#include <stdlib.h>


// 定义二叉树节点结构
struct TreeNode {
    char data;
    struct TreeNode* left;
    struct TreeNode* right;
};

// 创建新节点
struct TreeNode* createNode(char data) {
    struct TreeNode* newNode = (struct TreeNode*)malloc(sizeof(struct TreeNode));
    newNode->data = data;
    newNode->left = NULL;
    newNode->right = NULL;
    return newNode;
}

// 先序遍历函数
void preorderTraversal(struct TreeNode* node) {
    if (node == NULL) {
        return;
    }
    printf("%c ", node->data); // 访问根节点
    preorderTraversal(node->left); // 遍历左子树
    preorderTraversal(node->right); // 遍历右子树
}

int main() {
    // 创建一个简单的二叉树
    struct TreeNode* root = createNode('A');
    root->left = createNode('B');
    root->right = createNode('C');
    root->left->left = createNode('D');
    root->left->right = createNode('F');

    printf("先序遍历结果: ");
    preorderTraversal(root);
    printf("\n");

    // 释放内存
    free(root->left->left);
    free(root->left->right);
    free(root->left);
    free(root->right);
    free(root);

    return 0;
}
