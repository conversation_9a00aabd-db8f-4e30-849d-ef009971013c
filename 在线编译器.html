<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>在线 C++ 编译器 - 俄罗斯方块</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .compiler-link {
            display: block;
            margin: 15px 0;
            padding: 15px;
            background: #007acc;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            text-align: center;
            transition: background 0.3s;
        }
        .compiler-link:hover {
            background: #005a9e;
        }
        .instructions {
            background: #e8f4fd;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #007acc;
        }
        .code-preview {
            background: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 俄罗斯方块 - 在线编译器</h1>
        
        <div class="warning">
            <strong>⚠️ 注意：</strong> 由于俄罗斯方块游戏使用了控制台特定功能，在线编译器可能无法完美运行。建议安装本地编译器以获得最佳体验。
        </div>

        <div class="instructions">
            <h3>📋 使用说明：</h3>
            <ol>
                <li>点击下面任一在线编译器链接</li>
                <li>将 <code>俄罗斯方块.cpp</code> 的代码复制粘贴到编译器中</li>
                <li>点击 "运行" 或 "Run" 按钮</li>
                <li>注意：某些功能可能在在线环境中受限</li>
            </ol>
        </div>

        <h2>🌐 推荐的在线 C++ 编译器：</h2>

        <a href="https://www.onlinegdb.com/online_c++_compiler" class="compiler-link" target="_blank">
            🔗 OnlineGDB - 支持调试和多种语言
        </a>

        <a href="https://replit.com/languages/cpp" class="compiler-link" target="_blank">
            🔗 Replit - 功能强大的在线IDE
        </a>

        <a href="https://www.programiz.com/cpp-programming/online-compiler/" class="compiler-link" target="_blank">
            🔗 Programiz - 简单易用
        </a>

        <a href="https://cpp.sh/" class="compiler-link" target="_blank">
            🔗 CPP.sh - 轻量级在线编译器
        </a>

        <a href="https://www.jdoodle.com/online-compiler-c++/" class="compiler-link" target="_blank">
            🔗 JDoodle - 支持多种编译器版本
        </a>

        <div class="instructions">
            <h3>💡 最佳实践建议：</h3>
            <ul>
                <li><strong>本地安装编译器</strong> - 获得最佳性能和完整功能</li>
                <li><strong>使用 MinGW-w64</strong> - Windows 用户的最佳选择</li>
                <li><strong>配置 VS Code</strong> - 专业的开发环境</li>
                <li><strong>学习命令行编译</strong> - 理解编译过程</li>
            </ul>
        </div>

        <h3>📄 代码预览 (前50行)：</h3>
        <div class="code-preview">
#include &lt;ctime&gt;
#include &lt;cstdio&gt;
#include &lt;cmath&gt;
#include &lt;cstdlib&gt;
#include &lt;iostream&gt;
#include &lt;cstring&gt;

#ifdef _WIN32
    #include &lt;windows.h&gt;
    #include &lt;conio.h&gt;
    // 禁用弃用函数警告
    #pragma warning(disable: 4996)
#else
    #include &lt;termios.h&gt;
    #include &lt;unistd.h&gt;
    #include &lt;sys/select.h&gt;
    #include &lt;fcntl.h&gt;
#endif

// 跨平台函数声明
#ifdef _WIN32
    void gotoxy(int x, int y);
    void color(int b);
    void hideCursor();
    int kbhit();
    int getch();
    void Sleep(int ms);
#else
    void gotoxy(int x, int y);
    void color(int b);
    void hideCursor();
    int kbhit();
    int getch();
    void Sleep(int ms);
    void system(const char* cmd);
#endif

// 函数声明
void set_difficulty(int level);
void drawBlock(int x, int y, int color);
void clearBlock(int x, int y);

// 游戏速度和难度设置
#define Speed 600
#define EASY_SPEED 800
#define NORMAL_SPEED 600
#define HARD_SPEED 400

// ... (更多代码请查看源文件)
        </div>

        <div class="instructions">
            <h3>🔧 如需本地编译器安装帮助：</h3>
            <p>请查看 <code>安装编译器指南.md</code> 文件，或运行 <code>run_tetris.bat</code> 获取详细指导。</p>
        </div>
    </div>
</body>
</html>
