@echo off
chcp 65001 >nul
title 俄罗斯方块游戏

echo.
echo ╔══════════════════════════════════════╗
echo ║            俄罗斯方块游戏            ║
echo ╚══════════════════════════════════════╝
echo.

REM 检查是否存在已编译的程序
if exist "俄罗斯方块.exe" (
    echo ✓ 找到已编译的程序，直接运行...
    echo.
    echo 启动游戏中...
    echo.
    "俄罗斯方块.exe"
    goto :end
)

echo 未找到可执行文件，正在编译...
echo.

REM 调用编译脚本
call compile_simple.bat "俄罗斯方块.cpp"

REM 检查编译是否成功
if exist "俄罗斯方块.exe" (
    echo.
    echo ✓ 编译成功！正在启动游戏...
    echo.
    echo ╔══════════════════════════════════════╗
    echo ║              游戏控制                ║
    echo ║  ↑: 旋转方块    ↓: 加速下落          ║
    echo ║  ←: 向左移动    →: 向右移动          ║
    echo ║  Space: 暂停    P: 切换难度          ║
    echo ║  ESC: 退出游戏                       ║
    echo ╚══════════════════════════════════════╝
    echo.
    echo 按任意键开始游戏...
    pause >nul
    echo.
    "俄罗斯方块.exe"
) else (
    echo.
    echo ❌ 编译失败！请检查错误信息。
    echo.
    echo 可能的解决方案：
    echo 1. 安装 MinGW-w64 编译器
    echo 2. 安装 Visual Studio Community
    echo 3. 使用在线编译器（打开 在线编译器.html）
    echo.
)

:end
echo.
echo 游戏结束，按任意键退出...
pause >nul
