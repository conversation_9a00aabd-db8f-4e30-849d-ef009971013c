@echo off
title 俄罗斯方块游戏
chcp 65001 >nul

echo.
echo ========================================
echo           俄罗斯方块游戏
echo ========================================
echo.

REM 检查是否存在可执行文件
if exist "tetris.exe" (
    echo 启动游戏...
    echo.
    echo 游戏控制:
    echo   ↑: 旋转方块
    echo   ↓: 加速下落
    echo   ←: 向左移动
    echo   →: 向右移动
    echo   Space: 暂停游戏
    echo   ESC: 退出游戏
    echo.
    echo 按任意键开始...
    pause >nul
    tetris.exe
    goto end
)

echo 正在编译游戏...
g++ -std=c++11 -static-libgcc -static-libstdc++ -o tetris.exe tetris.cpp

if exist "tetris.exe" (
    echo 编译成功！启动游戏...
    tetris.exe
) else (
    echo 编译失败！请检查编译器是否正确安装。
    echo.
    echo 如需帮助，请运行: 安装编译器.bat
)

:end
echo.
echo 游戏结束，按任意键退出...
pause >nul
