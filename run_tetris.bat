@echo off
chcp 65001 >nul
echo ================================
echo     俄罗斯方块游戏启动器
echo ================================
echo.

REM 检查是否存在可执行文件
if exist "俄罗斯方块.exe" (
    echo 找到已编译的程序，直接运行...
    echo.
    "俄罗斯方块.exe"
    goto :end
)

echo 正在检查编译器...

REM 检查 g++ 编译器
where g++ >nul 2>nul
if %errorlevel% equ 0 (
    echo 找到 g++ 编译器，正在编译...
    g++ -std=c++11 -Wall -Wextra -static-libgcc -static-libstdc++ -o "俄罗斯方块.exe" "俄罗斯方块.cpp"
    if %errorlevel% equ 0 (
        echo 编译成功！启动游戏...
        echo.
        "俄罗斯方块.exe"
        goto :end
    ) else (
        echo 编译失败！
        goto :error
    )
)

REM 检查 cl 编译器 (Visual Studio)
where cl >nul 2>nul
if %errorlevel% equ 0 (
    echo 找到 cl 编译器，正在编译...
    cl /EHsc /Fe:"俄罗斯方块.exe" "俄罗斯方块.cpp"
    if %errorlevel% equ 0 (
        echo 编译成功！启动游戏...
        echo.
        "俄罗斯方块.exe"
        goto :end
    ) else (
        echo 编译失败！
        goto :error
    )
)

:error
echo.
echo ❌ 错误：未找到可用的 C++ 编译器！
echo.
echo 请安装以下任一编译器：
echo 1. MinGW-w64: https://www.mingw-w64.org/downloads/
echo 2. Visual Studio: https://visualstudio.microsoft.com/zh-hans/vs/community/
echo 3. Dev-C++: https://sourceforge.net/projects/orwelldevcpp/
echo.
echo 安装后请确保编译器在系统 PATH 中。
echo.

:end
echo.
echo 按任意键退出...
pause >nul
