# PowerShell script to compile and run Tetris
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "           俄罗斯方块编译器           " -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

$sourceFile = "俄罗斯方块.cpp"
$outputFile = "tetris.exe"

if (Test-Path $outputFile) {
    Write-Host "✓ 找到已编译的程序，直接运行..." -ForegroundColor Green
    & ".\$outputFile"
    exit
}

Write-Host "正在检测编译器..." -ForegroundColor Yellow

# Try g++
try {
    $null = Get-Command g++ -ErrorAction Stop
    Write-Host "✓ 找到 g++ 编译器" -ForegroundColor Green
    Write-Host "正在编译..." -ForegroundColor Yellow
    
    & g++ -std=c++11 -Wall -Wextra -static-libgcc -static-libstdc++ -o $outputFile $sourceFile
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ 编译成功！" -ForegroundColor Green
        Write-Host "启动游戏..." -ForegroundColor Yellow
        & ".\$outputFile"
        exit
    } else {
        Write-Host "✗ g++ 编译失败" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ 未找到 g++ 编译器" -ForegroundColor Red
}

# Try cl
try {
    $null = Get-Command cl -ErrorAction Stop
    Write-Host "✓ 找到 Visual Studio 编译器" -ForegroundColor Green
    Write-Host "正在编译..." -ForegroundColor Yellow
    
    & cl /EHsc /Fe:$outputFile $sourceFile 2>$null
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ 编译成功！" -ForegroundColor Green
        Write-Host "启动游戏..." -ForegroundColor Yellow
        # Clean up obj files
        Remove-Item *.obj -ErrorAction SilentlyContinue
        & ".\$outputFile"
        exit
    } else {
        Write-Host "✗ cl 编译失败" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ 未找到 Visual Studio 编译器" -ForegroundColor Red
}

Write-Host ""
Write-Host "❌ 错误：未找到可用的 C++ 编译器！" -ForegroundColor Red
Write-Host ""
Write-Host "请安装以下任一编译器：" -ForegroundColor Yellow
Write-Host "1. MinGW-w64: https://www.mingw-w64.org/downloads/" -ForegroundColor White
Write-Host "2. Visual Studio: https://visualstudio.microsoft.com/zh-hans/vs/community/" -ForegroundColor White
Write-Host "3. 运行 安装编译器.bat 获取帮助" -ForegroundColor White
Write-Host ""

Read-Host "按 Enter 键退出"
